../Scripts/f2py.exe,sha256=Nuwqd9iTippf8PX3dj8RSyN2CkuSs6mbFsnZBR5-nhQ,108375
../Scripts/numpy-config.exe,sha256=rbaDb8dmaveoox9QzfnHCSJl9iUfNrOGWHOzkj-3Aa8,108375
numpy-2.2.5-cp313-cp313-win_amd64.whl,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy-2.2.5.dist-info/DELVEWHEEL,sha256=yA4L2DocBqSmGAGYy1e136BjU2zGyIjOh0x975mmPYM,446
numpy-2.2.5.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
numpy-2.2.5.dist-info/LICENSE.txt,sha256=FCVsw6LJ0yrChNqWuTf-tE9y3ZC-4jF6wwIBZoRq2Z0,47709
numpy-2.2.5.dist-info/METADATA,sha256=HUkV87vN7MofIyrgafDovg3IjwtVKgDwjiuGPBC6jBI,60844
numpy-2.2.5.dist-info/RECORD,,
numpy-2.2.5.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy-2.2.5.dist-info/WHEEL,sha256=suq8ARrxbiI7iLH3BgK-82uzxQ-4Hm-m8w01oCokrtA,85
numpy-2.2.5.dist-info/entry_points.txt,sha256=4mXDNhJDQ9GHqMBeRJ8B3PlixTFmkXGqU3RVuac20q0,172
numpy.libs/libscipy_openblas64_-43e11ff0749b8cbe0a615c9cf6737e0e.dll,sha256=Y0aHyl_t73bnhNjFT8S6L5ZaRMC2DXVMSRdFid6rgVc,20301824
numpy.libs/msvcp140-263139962577ecda4cd9469ca360a746.dll,sha256=pMIim9wqKmMKzcCVtNhgCOXD47x3cxdDVPPaT1vrnN4,575056
numpy/__config__.py,sha256=b9waj_PNmyxi3T3vC9OPP4eXFliE9CW-4bru_DJvoQY,5694
numpy/__config__.pyi,sha256=b1FAG-TOWL9zaaazZoHWMp2OogmcZSTn3mkUJ5MRh3A,2479
numpy/__init__.cython-30.pxd,sha256=bwqZBDOkt5Nce2EmeHd9mV1eGoLTSoctn2n-lCWKWxc,48041
numpy/__init__.pxd,sha256=HUXemn5QF2YGUqOvJmqmFYpWZ64RPv5GwS66LG1eu5g,44591
numpy/__init__.py,sha256=7hWQTVK6pC7OFv_2OtWAL20YBVI8vN7lgo2YnkTdEnM,23016
numpy/__init__.pyi,sha256=d3zY0U5FGzmf-OimA-Ok3e3nQf7TM25F6uEZsC1dDN4,217782
numpy/__pycache__/__config__.cpython-313.pyc,,
numpy/__pycache__/__init__.cpython-313.pyc,,
numpy/__pycache__/_array_api_info.cpython-313.pyc,,
numpy/__pycache__/_configtool.cpython-313.pyc,,
numpy/__pycache__/_distributor_init.cpython-313.pyc,,
numpy/__pycache__/_expired_attrs_2_0.cpython-313.pyc,,
numpy/__pycache__/_globals.cpython-313.pyc,,
numpy/__pycache__/_pytesttester.cpython-313.pyc,,
numpy/__pycache__/conftest.cpython-313.pyc,,
numpy/__pycache__/ctypeslib.cpython-313.pyc,,
numpy/__pycache__/dtypes.cpython-313.pyc,,
numpy/__pycache__/exceptions.cpython-313.pyc,,
numpy/__pycache__/matlib.cpython-313.pyc,,
numpy/__pycache__/version.cpython-313.pyc,,
numpy/_array_api_info.py,sha256=Qd_2x_pUQLdBtnPKodEZy2Zds-R5i2DKQacMmMVRaRk,10727
numpy/_array_api_info.pyi,sha256=Y7SGdw5yxh4JQGeavwCaN2fpR7DR0KzU8GoOn7SKoiw,5102
numpy/_configtool.py,sha256=CgdDWSv9AX6XNKIibBXBisvuCu0aUkVVKbNudJfERIw,1046
numpy/_configtool.pyi,sha256=IlC395h8TlcZ4DiSW5i6NBQO9I74ERfXpwSYAktzoaU,25
numpy/_core/__init__.py,sha256=ziVwv-eSrrG6jAQYH3eQcPtNsdRZaWBnvzKCj4MrtbA,5792
numpy/_core/__init__.pyi,sha256=C5NQDIktXlR1OosGgyvY87pyotkyJr3Ci2dMWTLpSi4,88
numpy/_core/__pycache__/__init__.cpython-313.pyc,,
numpy/_core/__pycache__/_add_newdocs.cpython-313.pyc,,
numpy/_core/__pycache__/_add_newdocs_scalars.cpython-313.pyc,,
numpy/_core/__pycache__/_asarray.cpython-313.pyc,,
numpy/_core/__pycache__/_dtype.cpython-313.pyc,,
numpy/_core/__pycache__/_dtype_ctypes.cpython-313.pyc,,
numpy/_core/__pycache__/_exceptions.cpython-313.pyc,,
numpy/_core/__pycache__/_internal.cpython-313.pyc,,
numpy/_core/__pycache__/_machar.cpython-313.pyc,,
numpy/_core/__pycache__/_methods.cpython-313.pyc,,
numpy/_core/__pycache__/_string_helpers.cpython-313.pyc,,
numpy/_core/__pycache__/_type_aliases.cpython-313.pyc,,
numpy/_core/__pycache__/_ufunc_config.cpython-313.pyc,,
numpy/_core/__pycache__/arrayprint.cpython-313.pyc,,
numpy/_core/__pycache__/cversions.cpython-313.pyc,,
numpy/_core/__pycache__/defchararray.cpython-313.pyc,,
numpy/_core/__pycache__/einsumfunc.cpython-313.pyc,,
numpy/_core/__pycache__/fromnumeric.cpython-313.pyc,,
numpy/_core/__pycache__/function_base.cpython-313.pyc,,
numpy/_core/__pycache__/getlimits.cpython-313.pyc,,
numpy/_core/__pycache__/memmap.cpython-313.pyc,,
numpy/_core/__pycache__/multiarray.cpython-313.pyc,,
numpy/_core/__pycache__/numeric.cpython-313.pyc,,
numpy/_core/__pycache__/numerictypes.cpython-313.pyc,,
numpy/_core/__pycache__/overrides.cpython-313.pyc,,
numpy/_core/__pycache__/printoptions.cpython-313.pyc,,
numpy/_core/__pycache__/records.cpython-313.pyc,,
numpy/_core/__pycache__/shape_base.cpython-313.pyc,,
numpy/_core/__pycache__/strings.cpython-313.pyc,,
numpy/_core/__pycache__/umath.cpython-313.pyc,,
numpy/_core/_add_newdocs.py,sha256=eQ_QDKS8SuavunWLZh9rz0QtRVtzDtrIhmc3OAgodXw,215729
numpy/_core/_add_newdocs.pyi,sha256=ttPc9PlJ6lBkZrBrjzzWD4_jxmkIxpojL8RWR-d3e1c,171
numpy/_core/_add_newdocs_scalars.py,sha256=TeVoRpAbqG46cLwGVf-PRK-cIt9qgAKstAI-nHs8abg,12992
numpy/_core/_add_newdocs_scalars.pyi,sha256=qgD9RUeJdv6bkYewvQPXXCzO_roSKbaueq9PyvS6wSA,589
numpy/_core/_asarray.py,sha256=3wUlbaCM-agtm5HVRzD6T2xiqNpafdZ77QVkgb-HCAw,4047
numpy/_core/_asarray.pyi,sha256=vuCMO_o0RNeK0av8O5fvo93YOdxjJ2kgFeaw3GDobpY,1126
numpy/_core/_dtype.py,sha256=itXloCOgln5qr5mMFvGA54AEpC0ueUA3qiEH6Z798O0,11108
numpy/_core/_dtype.pyi,sha256=fVZoHORimwm-ck_pKiUx1RJvtSZoF7d5QxdGZI3ebVI,2009
numpy/_core/_dtype_ctypes.py,sha256=ebN9U_QbymSP-ombYBYc4F7HtgC3ViucNW91MqpNhrM,3838
numpy/_core/_dtype_ctypes.pyi,sha256=d5BudSdtj6n046OX9c-rUoX5zVGghdoO22yEhkjVRoM,3765
numpy/_core/_exceptions.py,sha256=35d-to48ERMggcjK60hKzHYhZJUUAxWY1GcJWh9bPJE,5551
numpy/_core/_exceptions.pyi,sha256=g4N5rEZf25Fbpu3AKAJn9c5MTlj671zZ6zWqPTd1Dnk,2219
numpy/_core/_internal.py,sha256=f7PNtQIywHQYg7rGnL7Wgo27Wwswcwl1i5tlRKnjgmw,30127
numpy/_core/_internal.pyi,sha256=sKos4TSABLgaoK1w_l06DewqULMQQIcNfQl6OPYBKPk,2726
numpy/_core/_machar.py,sha256=TWlW2yOKVA7Vk-s9gusxRgumvgTdCcAPL_72k8SROd4,11921
numpy/_core/_machar.pyi,sha256=g4N5rEZf25Fbpu3AKAJn9c5MTlj671zZ6zWqPTd1Dnk,2219
numpy/_core/_methods.py,sha256=QQaL40BLBbWChnmVaD9zYZtBqfxyufDMeYP1X1MTEUY,9725
numpy/_core/_methods.pyi,sha256=J9wblAExV__OQipgX4HbG74DOK5p4Ec1I31yNwv5WWg,580
numpy/_core/_multiarray_tests.cp313-win_amd64.lib,sha256=0qltFmDijs2NGUO1LiHxUNo08PXiTXBEVN0dLgjleaI,2418
numpy/_core/_multiarray_tests.cp313-win_amd64.pyd,sha256=EFb6nms-JMCrh3AsFCrqfPfwPiEnxEfGKQyo4fXl5W8,63488
numpy/_core/_multiarray_umath.cp313-win_amd64.lib,sha256=85FmS-czkWi5ivL4ECFpJ8OzCXpKs6MkGDgkjLQOXlc,2192
numpy/_core/_multiarray_umath.cp313-win_amd64.pyd,sha256=GEp-c-d9fjDPbageuIihWVYRVKeHW_SvchaowKOPwKA,4176896
numpy/_core/_operand_flag_tests.cp313-win_amd64.lib,sha256=ozPhoA5akD5sPujnk8i6jauxlZgVgYxj6xRWv0WzKa4,2228
numpy/_core/_operand_flag_tests.cp313-win_amd64.pyd,sha256=chdCkdTwUR1dhRf1MTWx_pr4Wx_rDKQjBjRbSifIIm8,11776
numpy/_core/_rational_tests.cp313-win_amd64.lib,sha256=GwnIVNxPv4d8HYjKZiD5NzePKI3_3eDA25TrIPeCq1o,2156
numpy/_core/_rational_tests.cp313-win_amd64.pyd,sha256=sz13uHd5xfrTuCzH0K3fh9MNZPerF1b1WEcZ5JVamUA,40448
numpy/_core/_simd.cp313-win_amd64.lib,sha256=ElUhc1ETeZmUTFjOnYuHCszZWETI98TNN6XgQtuVl9M,1976
numpy/_core/_simd.cp313-win_amd64.pyd,sha256=srGEnbUDL1XbpMf3FU7kXwyJFZYTqYceO5phtUvEnOI,2236928
numpy/_core/_simd.pyi,sha256=RN-uZiTi3jZNOgOOKlu97Mu1Ufkb8jvLUDDEnaW77Lc,694
numpy/_core/_string_helpers.py,sha256=yqhYXnS3SgnP_4PvP7NUYvYJ7c5GeFJz8a8zI_uU0DI,2937
numpy/_core/_string_helpers.pyi,sha256=bThH7ichGlrmQ6O38n71QcJ7Oi_mRPUf-DFMU5hdYU0,370
numpy/_core/_struct_ufunc_tests.cp313-win_amd64.lib,sha256=JQw2wRTTHnL-bGj9RgL5_PNokiYAMdplD6OyaUf6QE0,2228
numpy/_core/_struct_ufunc_tests.cp313-win_amd64.pyd,sha256=wIgVPB5f_7Ao1o-L8vo9VmoDRCFyGdCwqPXNEDF8NDQ,13824
numpy/_core/_type_aliases.py,sha256=uUDC8quSr11Ld3MRI0-4Rm1fiX1GFvu_rn1PVADuoKE,3608
numpy/_core/_type_aliases.pyi,sha256=YU1X6HEVmLcxqwswoZ_vdckwXcNPuyNhRxZgHAubMdA,2496
numpy/_core/_ufunc_config.py,sha256=82EbRK2pOLuW9YZRbepA0u8-_xzuYbDi2Y0rHv5Lq1k,15513
numpy/_core/_ufunc_config.pyi,sha256=L0fOlCDoajz-jkPfFDa7xo28o_GJDCsm4GjgpSi3e_Y,1211
numpy/_core/_umath_tests.cp313-win_amd64.lib,sha256=KilSBgG4HYONxhCElrajbzfmP1h0K5cQKl5Q300fqEI,2104
numpy/_core/_umath_tests.cp313-win_amd64.pyd,sha256=huJwFBfVKUcD4O90Du5IwwCC4xFigLKb_-Y7YtOiuDY,34304
numpy/_core/arrayprint.py,sha256=OY0l5ex3zDsf_sVW1XG2_LNX3779nOLWjcDY78e7n4Y,66568
numpy/_core/arrayprint.pyi,sha256=DGL2dfAhzXXOzOCIe_dEErom1cKMVOW-TB_18WnQfho,7163
numpy/_core/cversions.py,sha256=FISv1d4R917Bi5xJjKKy8Lo6AlFkV00WvSoB7l3acA4,360
numpy/_core/defchararray.py,sha256=eOGMMdrk4pVbuGvv3EBjjcGBNusWrHK4tNVIpJO5Z7I,39212
numpy/_core/defchararray.pyi,sha256=gIu3J0lelyWehaqPwUFwxgxlLtwSLMJWTVctGLS1yck,28058
numpy/_core/einsumfunc.py,sha256=bsFwrvIcvknVoQuaTk91125O-HApybSIvEPahQD8VxE,54319
numpy/_core/einsumfunc.pyi,sha256=fef7KF6KWLIhmBdbyDXt1J7g1C1QsMf0P05JKI7FGE4,5114
numpy/_core/fromnumeric.py,sha256=0X8Dqwd61a4aovHetN0_zzIsac7pW7JOw7UK7UXrDVI,148176
numpy/_core/fromnumeric.pyi,sha256=rwqQrnix67jCJ8I7YuIlVq_z-Kg_3UdBruLvIdTI4vI,42923
numpy/_core/function_base.py,sha256=x3yPOA2m9lZMNC0qJrUNO65ddzgoISNe2mqzgJ0BIBM,20279
numpy/_core/function_base.pyi,sha256=2wwLp3M55Vd6DCNCWzgk_DClf_0tOKyY5TbyNLE5YK0,5925
numpy/_core/getlimits.py,sha256=d3G99QSPEYNn73Mzxab-v1RWwVzBp67V_TcXvJ6wbCs,26859
numpy/_core/getlimits.pyi,sha256=3u55btDSVkpbsnFxkCWqRY7LZ1WhGop_LAUnjJfOUR8,64
numpy/_core/include/numpy/__multiarray_api.c,sha256=Vc65MKuXE5761vVI9qdZkPyg3C5_k_ickum0Q04EOOA,13045
numpy/_core/include/numpy/__multiarray_api.h,sha256=SzcxgIDQ8m4Ds1fvlM9fQ8RuINJpcPLRKzpb9HFDtpw,62996
numpy/_core/include/numpy/__ufunc_api.c,sha256=NoTcyLqrAF8F3AE0TDvlDFS7DXuFJRpoINEaDnZWhys,1809
numpy/_core/include/numpy/__ufunc_api.h,sha256=Q36B7NKN8E6GLytefgBOGLfgRnt8ayO1Conr2QWlqkA,13506
numpy/_core/include/numpy/_neighborhood_iterator_imp.h,sha256=s5TK2aPpClbw4CbVJCij__hzoh5IgHIIZK0k6FKtqfc,1947
numpy/_core/include/numpy/_numpyconfig.h,sha256=mqDMFv5Vhk2nHXNf6TIWzz7ozrtc9aNaN8_LJZBYjX0,902
numpy/_core/include/numpy/_public_dtype_api_table.h,sha256=4ylG8s52kZEx__QODt_7Do8QitmhDSvTeZ7Lar0fOgo,4660
numpy/_core/include/numpy/arrayobject.h,sha256=ghWzloPUkSaVkcsAnBnpbrxtXeXL-mkzVGJQEHFxjnk,211
numpy/_core/include/numpy/arrayscalars.h,sha256=4TrsilxaUiH4mVCkElEPTM_C_8c67O9R4Whx-3QzDE4,4439
numpy/_core/include/numpy/dtype_api.h,sha256=cfQuPb0zrVqYFdWauOqbgdXR8rtm4DjNz2nbfSWvSRo,19718
numpy/_core/include/numpy/halffloat.h,sha256=qYgX5iQfNzXICsnd0MCRq5ELhhfFjlRGm1xXGimQm44,2029
numpy/_core/include/numpy/ndarrayobject.h,sha256=V5Zkf5a9vWyV8ZInBgAceBn7c9GK4aquhzeGTW_Sgls,12361
numpy/_core/include/numpy/ndarraytypes.h,sha256=R3CFlGdGUQNj7rEovi8zRkJoJMk0y8682cu1kX1soAA,66986
numpy/_core/include/numpy/npy_1_7_deprecated_api.h,sha256=eYbQlqb6mzJnUKuVfl2mmrMpvB3GN2rFgHazFO9CKT8,3858
numpy/_core/include/numpy/npy_2_compat.h,sha256=VxsRXAtDfLlXkvH-ErZRSuH49k9EjcFwcSUSfTPRzAU,8795
numpy/_core/include/numpy/npy_2_complexcompat.h,sha256=uW0iF-qMwQNn4PvIfWCrYce6b4OrYUO4BWu-VYYAZag,885
numpy/_core/include/numpy/npy_3kcompat.h,sha256=dV01ltbxntPY8cN7WAL4MX3KHeyCLeSBDQreDxs09aQ,10022
numpy/_core/include/numpy/npy_common.h,sha256=3njI4LhBxMZvkkdG3nLq0NZI7lNqx9dnvcTfCgEW0rI,37621
numpy/_core/include/numpy/npy_cpu.h,sha256=FSFhzOQ_lvcpGw-4Qtzqu5W8eiD6k_K351_9WiI5uTg,4837
numpy/_core/include/numpy/npy_endian.h,sha256=NZSi-5CbqZ92AUztILDJLBKP61-VQezmAatYTNLwRu8,2912
numpy/_core/include/numpy/npy_math.h,sha256=ItgOGoKdQan93epl_EPF9Rm6M5Mis6xW__PbPIZsENA,19492
numpy/_core/include/numpy/npy_no_deprecated_api.h,sha256=jIcjEP2AbovDTfgE-qtvdP51_dVGjVnEGBX86rlGSKE,698
numpy/_core/include/numpy/npy_os.h,sha256=j044vd1C1oCcW52r3htiVNhUaJSEqCjKrODwMHq3TU0,1298
numpy/_core/include/numpy/numpyconfig.h,sha256=zDucznj2xbSOImOJLhjYHosMFqkoWY6AZvdRDpUrcw8,7339
numpy/_core/include/numpy/random/LICENSE.txt,sha256=1UR2FVi1EIZsIffootVxb8p24LmBF-O2uGMU23JE0VA,1039
numpy/_core/include/numpy/random/bitgen.h,sha256=_H0uXqmnub4PxnJWdMWaNqfpyFDu2KB0skf2wc5vjUc,508
numpy/_core/include/numpy/random/distributions.h,sha256=GLURa3sFESZE0_0RK-3Gqmfa96itBHw8LlsNyy9EPt4,10070
numpy/_core/include/numpy/random/libdivide.h,sha256=F9PLx6TcOk-sd0dObe0nWLyz4HhbHv2K7voR_kolpGU,82217
numpy/_core/include/numpy/ufuncobject.h,sha256=PO10lEoSvptYe57rrGpMiF0tytnmq9PW2UeMIwN06MY,12265
numpy/_core/include/numpy/utils.h,sha256=vzJAbatJYfxHmX2yL_xBirmB4mEGLOhJ92JlV9s8yPs,1222
numpy/_core/lib/npy-pkg-config/mlib.ini,sha256=hYWFyoBxE036dh19si8UPka01H2cv64qlc4ZtgoA_7A,156
numpy/_core/lib/npy-pkg-config/npymath.ini,sha256=e0rdsb00Y93VuammuvIIFlzZtnUAXwsS1XNKlCU8mFQ,381
numpy/_core/lib/npymath.lib,sha256=MB9a7L_emxqUHmgfAz8AQQiM9kBdQTw2v9QH9xWUVR4,154174
numpy/_core/lib/pkgconfig/numpy.pc,sha256=OfqoWh0L4-MyoQBagVOB1dHnSfcOyiO3Lo3FKmm4Z2k,198
numpy/_core/memmap.py,sha256=gtXDgEHkmzuCjwPpYFT5gkFzziYC41JAGYuKNDo2CfI,13025
numpy/_core/memmap.pyi,sha256=n0kBe4iQD5lcWvAvVhdUU18YIoPX6Sf5e2qh9IdO5uQ,50
numpy/_core/multiarray.py,sha256=-cH3HHzztYiSSESwdN7zcgqXk1L_AREuimMRJBk_cTE,59891
numpy/_core/multiarray.pyi,sha256=YEdOhn2NyCGHLQRgpe1Gd45xBHFlJSddPsHiesSWmAM,34751
numpy/_core/numeric.py,sha256=81YhZQ2LpEvj5Unv5EUlbXWktmkGR9dN2CvOWEjYukE,84439
numpy/_core/numeric.pyi,sha256=JVDzjcZBz_VOzHxL9cYux2LPrvq9Fg7ZI6fOiLciiAA,20094
numpy/_core/numerictypes.py,sha256=o2lhl1SFpGuDeIGFzfgS2zLWDwIAt41gi3VphVd9Cwg,16754
numpy/_core/numerictypes.pyi,sha256=Nuq8mEo62a0A1-6HJNTrONN7b9hkUNSp-HTRP28rYkc,3750
numpy/_core/overrides.py,sha256=gHrmLDRjVyi3poY5qYVrsAP9QNvvhXtiW1ggx5gwgGs,7392
numpy/_core/overrides.pyi,sha256=eSG0Xzxm7wutARSaQ_mmHPRDX-xpFHM0TAVheebF3S8,1793
numpy/_core/printoptions.py,sha256=btxyfisjJ_7DB5JtZKAtaLYL9qmcmTnnJ8pHFcwn2Wc,1095
numpy/_core/printoptions.pyi,sha256=QE36MVL3BgqflyQuj6UOzywbnELMiLeyNz_1sALvOSU,622
numpy/_core/records.py,sha256=tQof8zOMoY8UXn4ZtoIU8O6lPj8pUsDQFCOkZRAat9A,37953
numpy/_core/records.pyi,sha256=9df4GE_hkYVI-IlqCI6cxJHtYvuQ8TUwsXSjM_U8fOI,9104
numpy/_core/shape_base.py,sha256=ZROh3EbNF1IuHLF_1ecJ0NXpwUf__RXw8M2bWC1IO0Q,33887
numpy/_core/shape_base.pyi,sha256=vIF5OAbTOYBsImf_9BdIlT115WNburRg2KAFUV9ZMIs,4720
numpy/_core/strings.py,sha256=ZAKXDmpIzKReWYaUl-F4h5Fht4VVJ-z0VQpRV-Wf0kc,47313
numpy/_core/strings.pyi,sha256=XfkkJ03nRnTkqZop0LKUE_K-jVhrA4RFh9z-LHGQMng,13270
numpy/_core/tests/__pycache__/_locales.cpython-313.pyc,,
numpy/_core/tests/__pycache__/_natype.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test__exceptions.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_abc.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_api.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_argparse.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_array_api_info.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_array_coercion.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_array_interface.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_arraymethod.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_arrayobject.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_arrayprint.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_casting_floatingpoint_errors.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_casting_unittests.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_conversion_utils.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_cpu_dispatcher.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_cpu_features.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_custom_dtypes.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_cython.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_datetime.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_defchararray.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_deprecations.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_dlpack.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_dtype.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_einsum.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_errstate.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_extint128.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_function_base.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_getlimits.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_half.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_hashtable.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_indexerrors.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_indexing.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_item_selection.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_limited_api.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_longdouble.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_machar.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_mem_overlap.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_mem_policy.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_memmap.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_multiarray.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_multithreading.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_nditer.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_nep50_promotions.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_numeric.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_numerictypes.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_overrides.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_print.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_protocols.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_records.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalar_ctors.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalar_methods.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarbuffer.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarinherit.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarmath.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_scalarprint.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_shape_base.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_simd.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_simd_module.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_stringdtype.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_strings.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_ufunc.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_umath.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_umath_accuracy.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_umath_complex.cpython-313.pyc,,
numpy/_core/tests/__pycache__/test_unicode.cpython-313.pyc,,
numpy/_core/tests/_locales.py,sha256=xsKJqT3ZZiJGLQbm4Xx1W2i9KLqx14oQE9wUa49PkJ8,2248
numpy/_core/tests/_natype.py,sha256=uVXHCahmyDbZZAaQ-OKqaWnOgJRIYRETU06drssSSP0,6457
numpy/_core/tests/data/astype_copy.pkl,sha256=lWSzCcvzRB_wpuRGj92spGIw-rNPFcd9hwJaRVvfWdk,716
numpy/_core/tests/data/generate_umath_validation_data.cpp,sha256=9TBdxpPo0djv1CKxQ6_DbGKRxIZVawitAm7AMmWKroI,6012
numpy/_core/tests/data/recarray_from_file.fits,sha256=NA0kliz31FlLnYxv3ppzeruONqNYkuEvts5wzXEeIc4,8640
numpy/_core/tests/data/umath-validation-set-README.txt,sha256=GfrkmU_wTjpLkOftWDuGayEDdV3RPpN2GRVQX61VgWI,982
numpy/_core/tests/data/umath-validation-set-arccos.csv,sha256=VUdQdKBFrpXHLlPtX2WYIK_uwkaXgky85CZ4aNuvmD4,62794
numpy/_core/tests/data/umath-validation-set-arccosh.csv,sha256=tbuOQkvnYxSyJf_alGk3Zw3Vyv0HO5dMC1hUle2hWwQ,62794
numpy/_core/tests/data/umath-validation-set-arcsin.csv,sha256=JPEWWMxgPKdNprDq0pH5QhJ2oiVCzuDbK-3WhTKny8o,62768
numpy/_core/tests/data/umath-validation-set-arcsinh.csv,sha256=fwuq25xeS57kBExBuSNfewgHb-mgoR9wUGVqcOXbfoI,61718
numpy/_core/tests/data/umath-validation-set-arctan.csv,sha256=nu33YyL-ALXSSF5cupCTaf_jTPLK_QyUfciNQGpffkY,61734
numpy/_core/tests/data/umath-validation-set-arctanh.csv,sha256=wHSKFY2Yvbv3fnmmfLqPYpjhkEM88YHkFVpZQioyBDw,62768
numpy/_core/tests/data/umath-validation-set-cbrt.csv,sha256=FFi_XxEnGrfJd7OxtjVFT6WFC2tUqKhVV8fmQfb0z8o,62275
numpy/_core/tests/data/umath-validation-set-cos.csv,sha256=ccDri5_jQ84D_kAmSwZ_ztNUPIhzhgycDtNsPB7m8dc,60497
numpy/_core/tests/data/umath-validation-set-cosh.csv,sha256=DnN6RGvKQHAWIofchmhGH7kkJej2VtNwGGMRZGzBkTQ,62298
numpy/_core/tests/data/umath-validation-set-exp.csv,sha256=mPhjF4KLe0bdwx38SJiNipD24ntLI_5aWc8h-V0UMgM,17903
numpy/_core/tests/data/umath-validation-set-exp2.csv,sha256=sD94pK2EAZAyD2fDEocfw1oXNw1qTlW1TBwRlcpbcsI,60053
numpy/_core/tests/data/umath-validation-set-expm1.csv,sha256=tyfZN5D8tlm7APgxCIPyuy774AZHytMOB59H9KewxEs,61728
numpy/_core/tests/data/umath-validation-set-log.csv,sha256=CDPky64PjaURWhqkHxkLElmMiI21v5ugGGyzhdfUbnI,11963
numpy/_core/tests/data/umath-validation-set-log10.csv,sha256=dW6FPEBlRx2pcS-7eui_GtqTpXzOy147il55qdP-8Ak,70551
numpy/_core/tests/data/umath-validation-set-log1p.csv,sha256=2aEsHVcvRym-4535CkvJTsmHywkt01ZMfmjl-d4fvVI,61732
numpy/_core/tests/data/umath-validation-set-log2.csv,sha256=aVZ7VMQ5urGOx5MMMOUmMKBhFLFE-U7y6DVCTeXQfo0,70546
numpy/_core/tests/data/umath-validation-set-sin.csv,sha256=GvPrQUEYMX1iB2zjbfK26JUJOxtqbfiRUgXuAO1QcP0,59981
numpy/_core/tests/data/umath-validation-set-sinh.csv,sha256=lc7OYcYWWpkxbMuRAWmogQ5cKi7EwsQ2ibiMdpJWYbw,61722
numpy/_core/tests/data/umath-validation-set-tan.csv,sha256=fn7Dr9s6rcqGUzsmyJxve_Z18J4AUaSm-uo2N3N_hfk,61728
numpy/_core/tests/data/umath-validation-set-tanh.csv,sha256=xSY5fgfeBXN6fal4XDed-VUcgFIy9qKOosa7vQ5v1-U,61728
numpy/_core/tests/examples/cython/__pycache__/setup.cpython-313.pyc,,
numpy/_core/tests/examples/cython/checks.pyx,sha256=sGva3PIcoahXBIF3QkBWPjVnEWHPGQM1ktNp8OYEwUo,8183
numpy/_core/tests/examples/cython/meson.build,sha256=EaUdTgpleUBROExDaFVMnWIYW4XDxFLFGK9ej_pTtQg,1311
numpy/_core/tests/examples/cython/setup.py,sha256=tPQ9m6dr48JSvLpgmV-aVnMWMV0islzlSrynB5yGYDY,894
numpy/_core/tests/examples/limited_api/__pycache__/setup.cpython-313.pyc,,
numpy/_core/tests/examples/limited_api/limited_api1.c,sha256=RcHe_nyyjv86gjF9E53cexQiGW-YNs8OGGqjrxCFhBc,363
numpy/_core/tests/examples/limited_api/limited_api2.pyx,sha256=4P5-yu0yr8NBa-TFtw4v30LGjccRroRAQFFLaztEK9I,214
numpy/_core/tests/examples/limited_api/limited_api_latest.c,sha256=drvrNSyOeF0Or0trDmayJWllTP7c4Nzpp9T0ydwPAGo,471
numpy/_core/tests/examples/limited_api/meson.build,sha256=yitMzLuGDhWCjyavpm5UEBrhwKnfXOVAxA3ZL7PlB0Q,1686
numpy/_core/tests/examples/limited_api/setup.py,sha256=N7kqsVp4iIE20IebigEJUW3nW2F0l6Vthb5qNvKHBmM,457
numpy/_core/tests/test__exceptions.py,sha256=gy7-mZq7XS5z_w-us4gRIzC0H7XqC_62xaQQmWqLzSw,2970
numpy/_core/tests/test_abc.py,sha256=u82wrSKXJ2V7AmNrh4klHxYiqOx0BYWJ4j7hqTMH--A,2275
numpy/_core/tests/test_api.py,sha256=bURvc6MoIHlij2SrsgiDyafAQ-AyQDFFmbIzr-yo8Es,23546
numpy/_core/tests/test_argparse.py,sha256=vPctuxToPkZMlbgjnzE924XkxXYUdBxlR6LsP2_-aQM,2914
numpy/_core/tests/test_array_api_info.py,sha256=7n9-LJv-wgAMVbfK1JG7dQAU2WBYQbO7yeN4rP38Ltg,3174
numpy/_core/tests/test_array_coercion.py,sha256=vG5HHfLgl1HcP6oemFxvpYqibS0eWqRAKCxLCiZBjaY,35744
numpy/_core/tests/test_array_interface.py,sha256=E6QR-DJYTJX_F-i70PakQMmvxzfSBD-W1rFve70MFTg,7986
numpy/_core/tests/test_arraymethod.py,sha256=b7DeRtgzSCTzoPiS1BT1Wwvpr31g_YP44Dd4V6uaR-U,3339
numpy/_core/tests/test_arrayobject.py,sha256=cQu4aDjyF6EgoiGe5UISyOHGx5QEkdGvbfCXVuKjHQ8,2671
numpy/_core/tests/test_arrayprint.py,sha256=RERzgbVQ3mumSGJZhYV9LesTkcsMH8TrHMBPmPwkBTg,50349
numpy/_core/tests/test_casting_floatingpoint_errors.py,sha256=FRRWJBppa5v1axij6L14ENmzoZS8R_SyJKgHiAFI2KQ,5228
numpy/_core/tests/test_casting_unittests.py,sha256=FCokQoS_56dOoBjq1WSp2UVE2NE9WS2w2u-4xNBQjMM,35126
numpy/_core/tests/test_conversion_utils.py,sha256=cz2WEiCYSEP9m_7RHa2pS8WW0PcWO0E-LvpLTO72PkE,6814
numpy/_core/tests/test_cpu_dispatcher.py,sha256=Bpb_ep7kT3OfNypV1pSOWCNlk8oT46kjZBEGS32qfCI,1597
numpy/_core/tests/test_cpu_features.py,sha256=GO_Uf6FAK2pX8kiI9R1Uv7oEFzuPVzp3hu3bm6cZCuU,15838
numpy/_core/tests/test_custom_dtypes.py,sha256=JEeRO7ykZSPYDTlKEV4EWjlMWJwEWieiCCsN4aEd0WA,11934
numpy/_core/tests/test_cython.py,sha256=bgJxvIlQlMxxFA-Hqlgqo7NIvEKPZ7FvnY0av-EBfoM,8923
numpy/_core/tests/test_datetime.py,sha256=3KyUxJqrgCRvUomM3Pxw_wCodRjWYRcp-92Bggiqk2U,124376
numpy/_core/tests/test_defchararray.py,sha256=iOO8AUBOwhadTI8UUlOw-tI0Dd0l4k1rLY9gWFuQLbw,31423
numpy/_core/tests/test_deprecations.py,sha256=y76kFkCfXImnwdjBA6WJSSKPtFl_d6K3O-hCRT6_LAQ,29255
numpy/_core/tests/test_dlpack.py,sha256=MKc9PdCk1Tboz8RpMbrZzLFwkTEEEj-wQ5kZ1NeHtNI,5990
numpy/_core/tests/test_dtype.py,sha256=aQLvZkJzZX5qt9uGfXilf-o8xlIQ8p-ZDBUczLQnED8,79365
numpy/_core/tests/test_einsum.py,sha256=xOIA5Co2_FxHkzr0b-acAW5f_FOtnGDhQvS4gB1mLdM,54119
numpy/_core/tests/test_errstate.py,sha256=MjV1p7tDq7LpAgIT730lMNVFsxN0RVK4CtRJgBkpVlk,4763
numpy/_core/tests/test_extint128.py,sha256=YKIX0q9ENW0qehJtdaAAB2sFG0me42U2yJmq0kK6xGQ,5863
numpy/_core/tests/test_function_base.py,sha256=1FGoTuZLK_r0567gNARFIXqLhIY6QA9mqkh6rMGMLNw,17950
numpy/_core/tests/test_getlimits.py,sha256=k9_TaYqBCL-OvYpyvWAoTxpCwZSpxYFvvr2R7vuPEeg,7180
numpy/_core/tests/test_half.py,sha256=7M6VWJnBU7pnpGuoZc1hiltB5-rn9PkDEXI-EmtNKSA,24880
numpy/_core/tests/test_hashtable.py,sha256=-Zl-uppJbc9kwPN_ZlxJMA76yAQKsgGmQQWI8Y-sxaM,1182
numpy/_core/tests/test_indexerrors.py,sha256=keWclNvFu3ukhVSXc97w3bJM8BvkOpul6kjNudf1F2Q,4858
numpy/_core/tests/test_indexing.py,sha256=jbYs0Mdj_4w1XRBnrBCbUzZR9o1vdT3qHvo0YE3-yas,56741
numpy/_core/tests/test_item_selection.py,sha256=zaGuMcTDsbCpQO1k9c9xuc4jUWhbArfn_1INfilf9hk,6623
numpy/_core/tests/test_limited_api.py,sha256=oz7wOz7VRbrsP_60SaCiMl69GZlz-3J4b6S_9GsjF7A,3404
numpy/_core/tests/test_longdouble.py,sha256=kcu2DpPuw-j0on0INw-LNMOjw4wuXI_fPbvn-9n-Oks,14285
numpy/_core/tests/test_machar.py,sha256=z0mwyf6ASFI-gtMemFAag-8eEXKjb12mZ1BSpLYA52Q,1099
numpy/_core/tests/test_mem_overlap.py,sha256=fZMHusU29yuYAdMqkmLcfj209q8xjaY23IxwBPSUnoE,30071
numpy/_core/tests/test_mem_policy.py,sha256=Avw90zmQ5zjIvecpG0hV50UcKMaxVYkbmWQSdNuT6iA,17109
numpy/_core/tests/test_memmap.py,sha256=4PvMpV7EpYuCAlPkO1s8TiME75_G_V1toBm0ADizLpY,8372
numpy/_core/tests/test_multiarray.py,sha256=Su3uKM4LhZuMRhn8kyrwGqg-CZe93GRgaPgm0hrBZBs,402650
numpy/_core/tests/test_multithreading.py,sha256=P6JP2x-YqSU6gnzLGtK2VJ1mWeoJP7i-zxPpU46EDxU,8899
numpy/_core/tests/test_nditer.py,sha256=VrX91QX1nd3pWNM8MqxwYkDk_7swfuW0IACB86FBNVA,134550
numpy/_core/tests/test_nep50_promotions.py,sha256=NbdzCpLbwWHWUXBk4JxM5FFIa-YqibgHV2rEawS3h2U,10354
numpy/_core/tests/test_numeric.py,sha256=ol8-6PemWKQAGxpNGIgb-HjAwA13GU3IX4CBY84Wdms,162700
numpy/_core/tests/test_numerictypes.py,sha256=hQ1YqasQ6mq--7fnKO08koqVrnsK2IwloWcdElKB7U4,23912
numpy/_core/tests/test_overrides.py,sha256=_FsqndcyRN3r0JOp3Nn7_xAZPvGEypReo9PAdofeavY,28733
numpy/_core/tests/test_print.py,sha256=HhOMC4roNrnXdncgpXPmFgsJWwcRpCc9u3KOIMSRxDw,7038
numpy/_core/tests/test_protocols.py,sha256=19yXLJKXmy1-ArAclZ-F7PtgN6LmIHbiPvg6Vi2PkJk,1234
numpy/_core/tests/test_records.py,sha256=beGD-yv67DC-eav0VNeGLh06uIMwKp3IDEV-i2KySN4,21074
numpy/_core/tests/test_regression.py,sha256=5msZd_gdmBisnQrvKEysm-Nwjz84stp0n_c-Oqjmt3k,98058
numpy/_core/tests/test_scalar_ctors.py,sha256=CrPYj6xo5A52VVqecc9S8Q0JQWPPyU2pND5KUNX_-pw,6923
numpy/_core/tests/test_scalar_methods.py,sha256=CQARDMdU_T8LBg1sAdJ6PmRalpAK2CFSMH37AvLCmW0,9388
numpy/_core/tests/test_scalarbuffer.py,sha256=0d8LgyIclgmi4wJM218frNDXa4V408McDFDkieBpJFo,5735
numpy/_core/tests/test_scalarinherit.py,sha256=0JukiC7eR6NwWZgFy-YBmAXYIaA2BmudgY3Rt8ziX-I,2693
numpy/_core/tests/test_scalarmath.py,sha256=2A6CgAaeFUEqUBo4beyWi6UBwA-eyaE_0Is364Rj3cQ,47796
numpy/_core/tests/test_scalarprint.py,sha256=7BJSHWTeVvKtzt_IUgCgYcSp8uwTxbUlplGmV2edNHE,21058
numpy/_core/tests/test_shape_base.py,sha256=rqKjKS69o6NPEtqVUvRm4vc2zlrJ87QEy4PipMDlMXI,31842
numpy/_core/tests/test_simd.py,sha256=1KRDlvrx6MGvBLcFvGESoN5DSxQq0GKcvZeSrtRFL1Y,49985
numpy/_core/tests/test_simd_module.py,sha256=s22tqYtgN0x-5B3HTXiGfIV2aTXyQQH18c1fYj6VRhg,4004
numpy/_core/tests/test_stringdtype.py,sha256=B8B5ZhCCwWAbMa0xKJ9k3R02nBrGys_NPDccDeOZeT4,59491
numpy/_core/tests/test_strings.py,sha256=G0gAIyOpSHUaFfEUl30h3JRouIw4fz42ibatl9Gcv00,52956
numpy/_core/tests/test_ufunc.py,sha256=CthK8X2cvLRuASHDY4N1OHgqcN1pp5s1JZnd0VBA52U,135606
numpy/_core/tests/test_umath.py,sha256=jgOcWeFjvQElbDJHdW0Eb1_IynBS2Kqp3Y2hpDmfUzM,198085
numpy/_core/tests/test_umath_accuracy.py,sha256=ZW-NBEcRBWtbjzhPmk9fSpN3skQBuMgEoHS87zLmedk,5593
numpy/_core/tests/test_umath_complex.py,sha256=ZRnJuFo6DQPz5tdUUZyHSamtaI2BFlLXzz6AtlILVIw,23912
numpy/_core/tests/test_unicode.py,sha256=Y5VSmuMrpzuN9lNGB9gNDkUCl1c6qhiQRp2_kOc2V50,13221
numpy/_core/umath.py,sha256=3_OTbmiMhaVNsxpH2xKV9l6za59rYXBry6gb1WgJaI0,2133
numpy/_core/umath.pyi,sha256=9o4EBYeibP9abowHQHuo0iuhbUnfTWw5c8utNmKEduo,2840
numpy/_distributor_init.py,sha256=ahBbZPz-mGZrmwx35FHQ26AiinST78FxvupiBBKGFp4,422
numpy/_distributor_init.pyi,sha256=CSrbSp2YYxHTxlX7R0nT3RpH7EloB1wIvo7YOA7QWy8,28
numpy/_expired_attrs_2_0.py,sha256=uPUSplSC6_x_NhynoAup3ZCf3ydt7MGrBNwJc2dBiL8,3983
numpy/_expired_attrs_2_0.pyi,sha256=ZHjc6ZjYC1jKXXwLh4wylr6P1bYnlQ75sUigDNqaXoA,1332
numpy/_globals.py,sha256=FWUxIto9hQ5Mi2NoxP6DeGpI3bgS8H9xq7jfzaVLtG0,3185
numpy/_globals.pyi,sha256=kst3Vm7ZbznOtHsPya0PzU0KbjRGZ8xhMmTNMafvT-4,297
numpy/_pyinstaller/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/_pyinstaller/__pycache__/__init__.cpython-313.pyc,,
numpy/_pyinstaller/__pycache__/hook-numpy.cpython-313.pyc,,
numpy/_pyinstaller/hook-numpy.py,sha256=GFGizYFjd9HsYMOtby7gew94CkvTrRW77ECGPNUgGGc,1429
numpy/_pyinstaller/hook-numpy.pyi,sha256=2Bcwj2FwR3bRdtm26pmpUELEhsiZ58tQv9Q7_1Yp3HU,362
numpy/_pyinstaller/tests/__init__.py,sha256=ZKqNjqlKw1pYiv57onbjDJnJdVrLawbZAcl-mPZzcSw,345
numpy/_pyinstaller/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/_pyinstaller/tests/__pycache__/pyinstaller-smoke.cpython-313.pyc,,
numpy/_pyinstaller/tests/__pycache__/test_pyinstaller.cpython-313.pyc,,
numpy/_pyinstaller/tests/pyinstaller-smoke.py,sha256=xt3dl_DjxuzVTPrqmVmMOZm5-24wBG2TxldQl78Xt1g,1175
numpy/_pyinstaller/tests/test_pyinstaller.py,sha256=31zWlvlAC2sfhdew97x8aDvcYUaV3Tc_0CwFk8pgKaM,1170
numpy/_pytesttester.py,sha256=6Ii-VI4uz3wiQ5pzNZKdvUT6LOoN868rNzVff7rTlAk,6525
numpy/_pytesttester.pyi,sha256=Cy1rd-sv9DvmAAEKREy9VI0hYTWVpA_MoBRVmzDyvcY,515
numpy/_typing/__init__.py,sha256=eXfdON-ITGAVjpprnjbZC9kvco7c-aAolc377D2lqWE,5201
numpy/_typing/__pycache__/__init__.cpython-313.pyc,,
numpy/_typing/__pycache__/_add_docstring.cpython-313.pyc,,
numpy/_typing/__pycache__/_array_like.cpython-313.pyc,,
numpy/_typing/__pycache__/_char_codes.cpython-313.pyc,,
numpy/_typing/__pycache__/_dtype_like.cpython-313.pyc,,
numpy/_typing/__pycache__/_extended_precision.cpython-313.pyc,,
numpy/_typing/__pycache__/_nbit.cpython-313.pyc,,
numpy/_typing/__pycache__/_nbit_base.cpython-313.pyc,,
numpy/_typing/__pycache__/_nested_sequence.cpython-313.pyc,,
numpy/_typing/__pycache__/_scalars.cpython-313.pyc,,
numpy/_typing/__pycache__/_shape.cpython-313.pyc,,
numpy/_typing/__pycache__/_ufunc.cpython-313.pyc,,
numpy/_typing/_add_docstring.py,sha256=YPYjlxfqC8kXM_amtLyKXJ4aSNKJcmLY-It_PnW1-l4,4148
numpy/_typing/_array_like.py,sha256=OQB9L3K3TYX6_PRCtvUfy8BgYR1vYCsIkgIqlCTlkkk,5757
numpy/_typing/_callable.pyi,sha256=fTq5cTBoaRZwl3jFCk_G1eYSEyxHrpCR5-VoQJMMzZM,12176
numpy/_typing/_char_codes.py,sha256=Qj3t7j_gjoy7ECmVZzz3b5nVMGJBKU5tjPZJ1peGozo,9000
numpy/_typing/_dtype_like.py,sha256=on_sUDNeC6eQ6Vrlsi4JSJAfxGmIj2M3zhOx0yJtN0Q,6213
numpy/_typing/_extended_precision.py,sha256=5PhjET4NkRp-LSgffJqfcZ1C5Cp-xERB14FNXfUvRkU,804
numpy/_typing/_nbit.py,sha256=4E8E67SkSewPvDR15I68KEOneF8gsc97mFEe9oYBcdQ,651
numpy/_typing/_nbit_base.py,sha256=nN822ixIvBtkyDptX_LESrXoDZ4jjym5ph2FU6APEnk,2980
numpy/_typing/_nested_sequence.py,sha256=CjG49p-dxretKeShOiyVvTqOoyM_mNyhXArIYY6nBh4,2697
numpy/_typing/_scalars.py,sha256=sKaaEEZqAQtiEijeuH4U5KPNpG7FYsBtGO73l9dti9Q,1058
numpy/_typing/_shape.py,sha256=3g0rNpZHxM7rPInBJMSGpbVD9Y0Lw1QtkFEN_yrWEeo,238
numpy/_typing/_ufunc.py,sha256=SxToNG-O5NumF_yV7JRzAXloNbokV8B8JZC_EcbeNFk,160
numpy/_typing/_ufunc.pyi,sha256=mhArbicCGnkzBdfyPxLwf6oyAPyR5T2C-jBOk9S3Vq8,27651
numpy/_utils/__init__.py,sha256=mO41ldWPOHJkTkY-acmJ_8wM89IKRv2lWwUe_5XlUWo,3379
numpy/_utils/__init__.pyi,sha256=Pmoon5FkvkkbGNr0Xzx6i6i8aoehSnLFlBXNmBRXwi0,769
numpy/_utils/__pycache__/__init__.cpython-313.pyc,,
numpy/_utils/__pycache__/_convertions.cpython-313.pyc,,
numpy/_utils/__pycache__/_inspect.cpython-313.pyc,,
numpy/_utils/__pycache__/_pep440.cpython-313.pyc,,
numpy/_utils/_convertions.py,sha256=vetZFqC1qB-Z9jvc7RKuU_5ETOaSbjhbKa-sVwYV8TU,347
numpy/_utils/_convertions.pyi,sha256=zkZfkdBk6-XcyD3zmr7E5sJbYasvyDCInUtWvrtjVhY,122
numpy/_utils/_inspect.py,sha256=bSIacEhHLtYjTXaMVp1XFPY2IZfybb5bg8X5dYgc8JM,7626
numpy/_utils/_inspect.pyi,sha256=H1QZ7zEgYyG9kwpfz8cEUhF_QfM96WVBoerQtJZNVDI,2326
numpy/_utils/_pep440.py,sha256=y5Oppq3Kxn2dH3EWBYSENv_j8XjGUXWvNAiNCEJ-euI,14556
numpy/_utils/_pep440.pyi,sha256=LdpDFW8iIj_bLbuTbvRr2XWmC9YS9lrpzLR7efqL2GU,3991
numpy/char/__init__.py,sha256=oQZSAOs7rHme6CxfdL9nraYRNI3NU18MjzQ4kQmK2kA,95
numpy/char/__init__.pyi,sha256=wolX_qE2bjsIcUfQrQzGjzkaqdMtuGWOVDA3q-2Jqj0,1650
numpy/char/__pycache__/__init__.cpython-313.pyc,,
numpy/compat/__init__.py,sha256=oqsQeYKpQuJpuTLqMkZX6ssqQfSXs0Joj_S8Ms9KSNU,756
numpy/compat/__pycache__/__init__.cpython-313.pyc,,
numpy/compat/__pycache__/py3k.cpython-313.pyc,,
numpy/compat/py3k.py,sha256=wcSRGrTokLPxLamRFwBnsWS9z5uAyzsMlTEnCWUqpWw,3946
numpy/compat/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/compat/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/conftest.py,sha256=gViKWIQaPzu9tKZPTt6rIv3LxtvEe9nxrUgPZVDE8UY,8978
numpy/core/__init__.py,sha256=_lpcaIqNg3TH53JE0JKVKD4X0DOTki2dSvQgjHj6Eek,1307
numpy/core/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/__pycache__/__init__.cpython-313.pyc,,
numpy/core/__pycache__/_dtype.cpython-313.pyc,,
numpy/core/__pycache__/_dtype_ctypes.cpython-313.pyc,,
numpy/core/__pycache__/_internal.cpython-313.pyc,,
numpy/core/__pycache__/_multiarray_umath.cpython-313.pyc,,
numpy/core/__pycache__/_utils.cpython-313.pyc,,
numpy/core/__pycache__/arrayprint.cpython-313.pyc,,
numpy/core/__pycache__/defchararray.cpython-313.pyc,,
numpy/core/__pycache__/einsumfunc.cpython-313.pyc,,
numpy/core/__pycache__/fromnumeric.cpython-313.pyc,,
numpy/core/__pycache__/function_base.cpython-313.pyc,,
numpy/core/__pycache__/getlimits.cpython-313.pyc,,
numpy/core/__pycache__/multiarray.cpython-313.pyc,,
numpy/core/__pycache__/numeric.cpython-313.pyc,,
numpy/core/__pycache__/numerictypes.cpython-313.pyc,,
numpy/core/__pycache__/overrides.cpython-313.pyc,,
numpy/core/__pycache__/records.cpython-313.pyc,,
numpy/core/__pycache__/shape_base.cpython-313.pyc,,
numpy/core/__pycache__/umath.cpython-313.pyc,,
numpy/core/_dtype.py,sha256=PcSCn7DCpgrvBjm-k4eCMcEiTnH-jPzQmh8FyzLVw9I,331
numpy/core/_dtype.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_dtype_ctypes.py,sha256=eiZNKCJbzZ1Ei9Tkd7Fffx8vWUsAKnFSK-5vza3vmEQ,359
numpy/core/_dtype_ctypes.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/core/_internal.py,sha256=HC1NrqDEgK-6M1M6-8ZTZSZF7xnIYPh_G_4j2BFBNLM,972
numpy/core/_multiarray_umath.py,sha256=vO49_4x5SYg-BST541l73RmBm7pkqbwlssmwsRSdU80,2151
numpy/core/_utils.py,sha256=dAaZtXVWhOEFiwmVsz8Mn77HsynMDKhZ7HkrjD1Q3vc,944
numpy/core/arrayprint.py,sha256=qo9GIfdEmW9foxvP0vtFLRaAlSbOoOGJU-hBlQ5hIlA,347
numpy/core/defchararray.py,sha256=-gCjc9ciILhSzAxtVXgiTwdpuNMD3R6p9tXHe_MLx9A,355
numpy/core/einsumfunc.py,sha256=LkCSjRQ3HIF4fdRz7uEgl-1TyeT0gtGV5y8x9cQYsZ0,347
numpy/core/fromnumeric.py,sha256=iQsih718r6QW80auPJbva99qeWfT5IK2S02sv4AFMUs,351
numpy/core/function_base.py,sha256=V_-tUGZfgjYzjZxvhLNRtVXV2_v12rJsvAGpDXbfq8w,359
numpy/core/getlimits.py,sha256=SQsTlDpDVz9AvFC-xvAJbhcm5svBD02qpE-HLgt17RA,343
numpy/core/multiarray.py,sha256=2K7g3jXbH7wqupSsyr5wP0YoQSpXlZab9uDDbJtz2Bk,816
numpy/core/numeric.py,sha256=nTvwcwAqkzCnYmqEt4J3dvqUodzXUlaI8H5YF5x65xg,370
numpy/core/numerictypes.py,sha256=jmQ9c1WrWxlx8ODDZKOAqrixUu3Gx_NJD1SzT3wtb50,355
numpy/core/overrides.py,sha256=Dq-lTb829gvg-HfRtY0BE6GE2UbI6iXkMIh8Gvkzt1g,343
numpy/core/overrides.pyi,sha256=HScieJk23k4Lk14q8u9CEc3ZEVOQ6hGu_FeWDR2Tyu8,532
numpy/core/records.py,sha256=5jPtgEtHaJ642Ct-G9uEwnF9y_TZnZAUXm_EUJEF8J8,335
numpy/core/shape_base.py,sha256=itirz4hN3M8Ndgij4_ZVcra4qtRkK42Owp8qr9fFe5w,347
numpy/core/umath.py,sha256=09uNybUqfWxdqkoYHzv6jrTDCXq6DDI-EdwaOKdijn4,327
numpy/ctypeslib.py,sha256=9ejyo77Qqd54f9j7pRQQaABYAfVxjWfgKvia88T9hP4,19438
numpy/ctypeslib.pyi,sha256=7CY_Na2E0uwZ88TzJ3pasogyLIr0wd8scdcxY0LK21A,8338
numpy/doc/__pycache__/ufuncs.cpython-313.pyc,,
numpy/doc/ufuncs.py,sha256=jMnfQhRknVIhgFVS9z2l5oYM8N1tuQtf5bXMBL449oI,5552
numpy/dtypes.py,sha256=cPkS6BLRvpfsUzhd7Vk1L7_VcenWb1nuHuCxc9fYC4I,1353
numpy/dtypes.pyi,sha256=9Gys5OIDUcglbDCgnJqubAljutcy_NUtmqWUy9-rRB0,15787
numpy/exceptions.py,sha256=8or6nB2di0rsXpxLrmoUI4nH5bsyAIInsBfMDYL1RS8,8085
numpy/exceptions.pyi,sha256=baBkfJ_DQdH6AH7roIXq8JSlY5Wn4z_hdJVbo_1SQUE,776
numpy/f2py/__init__.py,sha256=WZXe6JMmUBaRuBtosCRzno0roeUj8CEoQw9g2_RRokc,2590
numpy/f2py/__init__.pyi,sha256=0_-xXhZztqkodDS2UJTGZAdLO8JkzE7LMJYeDZa46cY,1103
numpy/f2py/__main__.py,sha256=TDesy_2fDX-g27uJt4yXIXWzSor138R2t2V7HFHwqAk,135
numpy/f2py/__pycache__/__init__.cpython-313.pyc,,
numpy/f2py/__pycache__/__main__.cpython-313.pyc,,
numpy/f2py/__pycache__/__version__.cpython-313.pyc,,
numpy/f2py/__pycache__/_isocbind.cpython-313.pyc,,
numpy/f2py/__pycache__/_src_pyf.cpython-313.pyc,,
numpy/f2py/__pycache__/auxfuncs.cpython-313.pyc,,
numpy/f2py/__pycache__/capi_maps.cpython-313.pyc,,
numpy/f2py/__pycache__/cb_rules.cpython-313.pyc,,
numpy/f2py/__pycache__/cfuncs.cpython-313.pyc,,
numpy/f2py/__pycache__/common_rules.cpython-313.pyc,,
numpy/f2py/__pycache__/crackfortran.cpython-313.pyc,,
numpy/f2py/__pycache__/diagnose.cpython-313.pyc,,
numpy/f2py/__pycache__/f2py2e.cpython-313.pyc,,
numpy/f2py/__pycache__/f90mod_rules.cpython-313.pyc,,
numpy/f2py/__pycache__/func2subr.cpython-313.pyc,,
numpy/f2py/__pycache__/rules.cpython-313.pyc,,
numpy/f2py/__pycache__/symbolic.cpython-313.pyc,,
numpy/f2py/__pycache__/use_rules.cpython-313.pyc,,
numpy/f2py/__version__.py,sha256=TisKvgcg4vh5Fptw2GS1JB_3bAQsWZIKhclEX6ZcAho,35
numpy/f2py/_backends/__init__.py,sha256=xIVHiF-velkBDPKwFS20PSg-XkFW5kLAVj5CSqNLddM,308
numpy/f2py/_backends/__pycache__/__init__.cpython-313.pyc,,
numpy/f2py/_backends/__pycache__/_backend.cpython-313.pyc,,
numpy/f2py/_backends/__pycache__/_distutils.cpython-313.pyc,,
numpy/f2py/_backends/__pycache__/_meson.cpython-313.pyc,,
numpy/f2py/_backends/_backend.py,sha256=9RZDu4FCwCM7G39EX2YEt-Vnaz0U2WSp-QSAfz11BGE,1233
numpy/f2py/_backends/_distutils.py,sha256=e3dqC9ddmppsCNhLngtOE3Z6WZnLfaG_N5xiIcHPVWI,2459
numpy/f2py/_backends/_meson.py,sha256=GD5pv3ilTRjtU4wGWgWrakg4nFySOiaX4NdmgO3egYM,8322
numpy/f2py/_backends/meson.build.template,sha256=6XD3j-K5pc1P_icgUWkrgEsyludQWsqS5rb6UB29tH0,1654
numpy/f2py/_isocbind.py,sha256=QVoR_pD_bY9IgTaSHHUw_8EBg0mkaf3JZfwhLfHbz1Q,2422
numpy/f2py/_src_pyf.py,sha256=3swmQKGTeQGVMLzTTkZqZHHQ5EP6RT2LjgaUnXv0S74,7904
numpy/f2py/auxfuncs.py,sha256=5tF_ZvesfJDTmh-1Pq7NgV7ArDtD2aOGhwbb4VZtraE,28020
numpy/f2py/capi_maps.py,sha256=Hj1g5T5Siyc4JWSZJPnbfXqPTCqoXblwiDET04UBh4k,31428
numpy/f2py/cb_rules.py,sha256=hALemKsqa1qkTD2KqBcdGmRDhSTAuq1Z5ZsPlJjWdXw,25648
numpy/f2py/cfuncs.py,sha256=qbuF9fJWlhVSZ3xIstFwrGWM7FO1Zy0DUzBk6HD11ik,54036
numpy/f2py/common_rules.py,sha256=19VDEPQ9-Pzzknv03U23gWYesmDAzJrGxwdXqn7CxhQ,5277
numpy/f2py/crackfortran.py,sha256=DnaLeMI4wdc9cUAUMSuCb_2_ZAzWuQfbi8puLeRNbeU,151841
numpy/f2py/diagnose.py,sha256=0DtPTDjxbFUu0F_nDHfsD0vlCgnRhf8WZ1kHsXVWcpE,5351
numpy/f2py/f2py2e.py,sha256=36qdKKlXxLiwFZoDwA9sYZMxH6IzoPY9alB8ZajnxDY,29621
numpy/f2py/f90mod_rules.py,sha256=Q-e9Q79dkOvEBLoJDLTf7nX7WbtPf-qt4pbRI41kLYw,10144
numpy/f2py/func2subr.py,sha256=Wro0C3NGSO-1g2zxBI8qg_Tl6KyczrCtCTJvhN4KtUQ,10621
numpy/f2py/rules.py,sha256=lQjZ-e0LAArXNmso5c6H_IqXZiDdmcmmgmV3tztJ4UI,64516
numpy/f2py/setup.cfg,sha256=828sy3JvJmMzVxLkC-y0lxcEMaDTnMc3l9dWqP4jYng,50
numpy/f2py/src/fortranobject.c,sha256=R7AJfWjQiz2dLylWtFpvZByWvu9OCkG4UCkMa3t-jxw,47472
numpy/f2py/src/fortranobject.h,sha256=uCcHO8mjuANlKb3c7YAZwM4pgT0CTaXWLYqgE27Mnt0,5996
numpy/f2py/symbolic.py,sha256=BI5m8j7wEpq1u9yTDUBUtqUCCH1JBVVxyEFZRMjGWlA,54771
numpy/f2py/tests/__init__.py,sha256=hiQX1lvI7rIYRNecVpg5D_0N6E0w94BSmexhEErutmI,343
numpy/f2py/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_abstract_interface.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_array_from_pyobj.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_assumed_shape.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_block_docstring.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_callback.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_character.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_common.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_crackfortran.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_data.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_docs.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_f2cmap.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_f2py2e.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_isoc.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_kind.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_mixed.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_modules.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_parameter.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_pyf_src.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_quoted_character.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_character.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_complex.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_integer.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_logical.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_return_real.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_routines.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_semicolon_split.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_size.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_string.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_symbolic.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/test_value_attrspec.cpython-313.pyc,,
numpy/f2py/tests/__pycache__/util.cpython-313.pyc,,
numpy/f2py/tests/src/abstract_interface/foo.f90,sha256=aCaFEqfXp79pVXnTFtjZBWUY_5pu8wsehp1dEauOkSE,692
numpy/f2py/tests/src/abstract_interface/gh18403_mod.f90,sha256=y3R2dDn0BUz-0bMggfT1jwXbhz_gniz7ONMpureEQew,111
numpy/f2py/tests/src/array_from_pyobj/wrapmodule.c,sha256=0UkctY5oeFs9B9qnX8qhe3wTFZA_mF-FBBkJoy_iuQg,7713
numpy/f2py/tests/src/assumed_shape/.f2py_f2cmap,sha256=zfuOShmuotzcLIQDnVFaARwvM66iLrOYzpquIGDbiKU,30
numpy/f2py/tests/src/assumed_shape/foo_free.f90,sha256=fqbSr7VlKfVrBulFgQtQA9fQf0mQvVbLi94e4FTST3k,494
numpy/f2py/tests/src/assumed_shape/foo_mod.f90,sha256=9pbi88-uSNP5IwS49Kim982jDAuopo3tpEhg2SOU7no,540
numpy/f2py/tests/src/assumed_shape/foo_use.f90,sha256=9Cl1sdrihB8cCSsjoQGmOO8VRv9ni8Fjr0Aku1UdEWM,288
numpy/f2py/tests/src/assumed_shape/precision.f90,sha256=3L_F7n5ju9F0nxw95uBUaPeuiDOw6uHvB580eIj7bqI,134
numpy/f2py/tests/src/block_docstring/foo.f,sha256=KVTeqSFpI94ibYIVvUW6lOQ9T2Bx5UzZEayP8Maf2H0,103
numpy/f2py/tests/src/callback/foo.f,sha256=rLqaaaUpWFTaGVxNoGERtDKGCa5dLCTW5DglsFIx-wU,1316
numpy/f2py/tests/src/callback/gh17797.f90,sha256=-_NvQK0MzlSR72PSuUE1FeUzzsMBUcPKsbraHIF7O24,155
numpy/f2py/tests/src/callback/gh18335.f90,sha256=n_Rr99cI7iHBEPV3KGLEt0QKZtItEUKDdQkBt0GKKy4,523
numpy/f2py/tests/src/callback/gh25211.f,sha256=ejY_ssadbZQfD5_-Xnx_ayzWXWLjkdy7DGp6C_uCUCY,189
numpy/f2py/tests/src/callback/gh25211.pyf,sha256=nrzvt2QHZRCcugg0R-4FDMMl1MJmWCOAjR7Ta-pXz7Y,465
numpy/f2py/tests/src/callback/gh26681.f90,sha256=ykwNXWyja5FfZk1bPihbYiMmMlbKhRPoPKva9dNFtLM,584
numpy/f2py/tests/src/cli/gh_22819.pyf,sha256=e3zYjFmiOxzdXoxzgkaQ-CV6sZ1t4aKugyhqRXmBNdQ,148
numpy/f2py/tests/src/cli/hi77.f,sha256=bgBERF4EYxHlzJCvZCJOlEmUE1FIvipdmj4LjdmL_dE,74
numpy/f2py/tests/src/cli/hiworld.f90,sha256=RncaEqGWmsH9Z8BMV-UmOTUyo3-e9xOQGAmNgDv6SfY,54
numpy/f2py/tests/src/common/block.f,sha256=tcGKa42S-6bfA6fybpM0Su_xjysEVustkEJoF51o_pE,235
numpy/f2py/tests/src/common/gh19161.f90,sha256=Vpb34lRVC96STWaJerqkDQeZf7mDOwWbud6pW62Tvm4,203
numpy/f2py/tests/src/crackfortran/accesstype.f90,sha256=3ONHb4ZNx0XISvp8fArnUwR1W9rzetLFILTiETPUd80,221
numpy/f2py/tests/src/crackfortran/common_with_division.f,sha256=JAzHD5aluoYw0jVGZjBYd1wTABU0PwNBD0cz3Av5AAk,511
numpy/f2py/tests/src/crackfortran/data_common.f,sha256=rP3avnulWqJbGCFLWayjoFKSspGDHZMidPTurjz33Tc,201
numpy/f2py/tests/src/crackfortran/data_multiplier.f,sha256=LaPXVuo5lX0gFZVh76Hc7LM1sMk9EBPALuXBnHAGdOA,202
numpy/f2py/tests/src/crackfortran/data_stmts.f90,sha256=MAZ3gstsPqECk3nWQ5Ql-C5udrIv3sAciW1ZGTtHLts,713
numpy/f2py/tests/src/crackfortran/data_with_comments.f,sha256=FUPluNth5uHgyKqjQW7HKmyWg4wDXj3XPJCIC9ZZuOs,183
numpy/f2py/tests/src/crackfortran/foo_deps.f90,sha256=D9FT8Rx-mK2p8R6r4bWxxqgYhkXR6lNmPj2RXOseMpw,134
numpy/f2py/tests/src/crackfortran/gh15035.f,sha256=0G9bmfVafpuux4-ZgktYZ6ormwrWDTOhKMK4wmiSZlQ,391
numpy/f2py/tests/src/crackfortran/gh17859.f,sha256=acknjwoWYdA038oliYLjB4T1PHhXkKRLeJobIgB_Lbo,352
numpy/f2py/tests/src/crackfortran/gh22648.pyf,sha256=xPnKx4RcT1568q-q_O83DYpCgVYJ8z4WQ-yLmHPchJA,248
numpy/f2py/tests/src/crackfortran/gh23533.f,sha256=k2xjRpRaajMYpi5O-cldYPTZGFGB12PUGcj5Fm9joyk,131
numpy/f2py/tests/src/crackfortran/gh23598.f90,sha256=20ukdZXq-qU0Zxzt4W6cO8tRxlNlQ456zgD09zdozCE,105
numpy/f2py/tests/src/crackfortran/gh23598Warn.f90,sha256=FvnIxy5fEOvzNb5WSkWzPk7yZ9yIv0yPZk9vNnS-83w,216
numpy/f2py/tests/src/crackfortran/gh23879.f90,sha256=jELVfEGEF66z_Pv_iBHp3yGsGhadB0dnKCDtPcaz_CM,352
numpy/f2py/tests/src/crackfortran/gh27697.f90,sha256=mTOEncxZlam6N-3I-IL0ua-iLkgqDrrVXNsE-7y7jAM,376
numpy/f2py/tests/src/crackfortran/gh2848.f90,sha256=-IpkeTz0j9_lkQeN9mT7w3U1cAJjQxSMdAmyHdF8oVg,295
numpy/f2py/tests/src/crackfortran/operators.f90,sha256=cb1JO2hIMCQejZO_UJWluBCP8LdXQbBJw2XN6YHB3JA,1233
numpy/f2py/tests/src/crackfortran/privatemod.f90,sha256=9O2oWEquIUcbDB1wIzNeae3hx4gvXAoYW5tGfBt3KWk,185
numpy/f2py/tests/src/crackfortran/publicmod.f90,sha256=nU_VXCKiniiUq_78KAWkXiN6oiMQh39emMxbgOVf9cg,177
numpy/f2py/tests/src/crackfortran/pubprivmod.f90,sha256=-uz75kquU4wobaAPZ1DLKXJg6ySCZoDME1ce6YZ2q5Y,175
numpy/f2py/tests/src/crackfortran/unicode_comment.f90,sha256=wDMoF7F7VFYdeocfTyWIh7noniEwExVb364HrhUSbSg,102
numpy/f2py/tests/src/f2cmap/.f2py_f2cmap,sha256=fwszymaWhcWO296u5ThHW5yMAkFhB6EtHWqqpc9FAVI,83
numpy/f2py/tests/src/f2cmap/isoFortranEnvMap.f90,sha256=rphN_mmzjCCCkdPM0HjsiJV7rmxpo4GoCNp5qmBzv8U,307
numpy/f2py/tests/src/isocintrin/isoCtests.f90,sha256=Oir0PfE3mErnUQ42aFxiqAkcYn3B6b1FHIPGipDdekg,1032
numpy/f2py/tests/src/kind/foo.f90,sha256=6_zq3OAWsuNJ5ftGTQAEynkHy-MnuLgBXmMIgbvL7yU,367
numpy/f2py/tests/src/mixed/foo.f,sha256=Zgn0xDhhzfas3HrzgVSxIL1lGEF2mFRVohrvXN1thU0,90
numpy/f2py/tests/src/mixed/foo_fixed.f90,sha256=6eEEYCH71gPp6lZ6e2afLrfS6F_fdP7GZDbgGJJ_6ns,187
numpy/f2py/tests/src/mixed/foo_free.f90,sha256=UC6iVRcm0-aVXAILE5jZhivoGQbKU-prqv59HTbxUJA,147
numpy/f2py/tests/src/modules/gh25337/data.f90,sha256=EqMEuEV0_sx4XbFzftbU_6VfGtOw9Tbs0pm0eVEp2cA,188
numpy/f2py/tests/src/modules/gh25337/use_data.f90,sha256=DChVLgD7qTOpbYNmfGjPjfOx5YsphMIYwdwnF12X4xM,185
numpy/f2py/tests/src/modules/gh26920/two_mods_with_no_public_entities.f90,sha256=MMLPSzBwuGS4UwCXws9djH11F5tG5xFLc80CDb4U9Mk,423
numpy/f2py/tests/src/modules/gh26920/two_mods_with_one_public_routine.f90,sha256=1dJD1kDC_wwn7v_zF49D3n62T1x9wFxGKanQQz_VI7k,424
numpy/f2py/tests/src/modules/module_data_docstring.f90,sha256=-asnMH7vZMwVIeMU2YiLWgYCUUUxZgPTpbAomgWByHs,236
numpy/f2py/tests/src/modules/use_modules.f90,sha256=bveSAqXIZtd4NMlDfFei1ZlesFAa9An5LjkD-gDk2ms,418
numpy/f2py/tests/src/negative_bounds/issue_20853.f90,sha256=IxBGWem-uv9eHgDhysEdGTmNKHR1gAiU7YJPo20eveM,164
numpy/f2py/tests/src/parameter/constant_array.f90,sha256=fkYemwIBKsP63-FGKBW8mzOAp6k13eZOin8sQe1pyno,1513
numpy/f2py/tests/src/parameter/constant_both.f90,sha256=L0rG6-ClvHx7Qsch46BUXRi_oIEL0uw5dpRHdOUQuv0,1996
numpy/f2py/tests/src/parameter/constant_compound.f90,sha256=lAT76HcXGMgr1NfKof-RIX3W2P_ik1PPqkRdJ6EyBmM,484
numpy/f2py/tests/src/parameter/constant_integer.f90,sha256=42jROArrG7vIag9wFa_Rr5DBnnNvGsrEUgpPU14vfIo,634
numpy/f2py/tests/src/parameter/constant_non_compound.f90,sha256=u9MRf894Cw0MVlSOUbMSnFSHP4Icz7RBO21QfMkIl-Q,632
numpy/f2py/tests/src/parameter/constant_real.f90,sha256=QoPgKiHWrwI7w5ctYZugXWzaQsqSfGMO7Jskbg4CLTc,633
numpy/f2py/tests/src/quoted_character/foo.f,sha256=0zXQbdaqB9nB8R4LF07KDMFDbxlNdiJjVdR8Nb3nzIM,496
numpy/f2py/tests/src/regression/AB.inc,sha256=ydjTVb6QEw1iYw2tRiziqqzWcDHrJsNWr3m51-rqFXQ,17
numpy/f2py/tests/src/regression/assignOnlyModule.f90,sha256=vPJbhOlNsLrgN3su4ohHUSbxE4GGKU7SiJh7dhBvX3o,633
numpy/f2py/tests/src/regression/datonly.f90,sha256=HuBLuEw0kNEplJ9TxxSNr7hLj-jx9ZNGaXC8iLm_kf8,409
numpy/f2py/tests/src/regression/f77comments.f,sha256=FjP-07suTBdqgtwiENT04P-47UB4g9J5-20IQdXAHhM,652
numpy/f2py/tests/src/regression/f77fixedform.f95,sha256=KdKFcAc3ZrID-h4nTOJDdEYfQzR2kkn9VqQCorfJGpM,144
numpy/f2py/tests/src/regression/f90continuation.f90,sha256=VweFIi5-xxZhtgSOh8i_FjMPXu_od9qjrDHq6ma5X5k,285
numpy/f2py/tests/src/regression/incfile.f90,sha256=gq87H2CtCZUON9V5UzcK6x_fthnWDVuPFQLa0fece1M,97
numpy/f2py/tests/src/regression/inout.f90,sha256=TlMxJjhjjiuLI--Tg2LshLnbfZpiKz37EpR_tPKKSx8,286
numpy/f2py/tests/src/regression/lower_f2py_fortran.f90,sha256=bWlj2Frch3onnUpd6DTaoLDa6htrrbkBiI9JIRbQPfE,105
numpy/f2py/tests/src/return_character/foo77.f,sha256=tRyQSu9vNWtMRi7gjmMN-IZnS7ogr5YS0n38uax_Eo0,1025
numpy/f2py/tests/src/return_character/foo90.f90,sha256=WPQZC6CjXLbUYpzy5LItEoHmRDFxW0ABB3emRACsjZU,1296
numpy/f2py/tests/src/return_complex/foo77.f,sha256=7-iKoamJ-VObPFR-Tslhiw9E-ItIvankWMyxU5HqxII,1018
numpy/f2py/tests/src/return_complex/foo90.f90,sha256=_GOKOZeooWp3pEaTBrZNmPmkgGodj33pJnJmySnp7aE,1286
numpy/f2py/tests/src/return_integer/foo77.f,sha256=EKs1KeAOQBkIO99tMCx0H7_lpqvqpjie8zWZ6T_bAR4,1234
numpy/f2py/tests/src/return_integer/foo90.f90,sha256=0aYWcaAVs7Lw3Qbf8hupfLC8YavRuPZVIwjHecIlMOo,1590
numpy/f2py/tests/src/return_logical/foo77.f,sha256=Ax3tBVNAlxFtHhV8fziFcsTnoa8YJdapecMr6Qj7fLk,1244
numpy/f2py/tests/src/return_logical/foo90.f90,sha256=IZXCerFecYT24zTQ_spIoPr6n-fRncaM0tkTs8JqO1E,1590
numpy/f2py/tests/src/return_real/foo77.f,sha256=3nAY1YtzGk4osR2jZkHMVIUHxFoOtF1OLfWswpcV7kA,978
numpy/f2py/tests/src/return_real/foo90.f90,sha256=38ZCnBGWb9arlJdnVWvZjVk8uesrQN8wG2GrXGcSIJs,1242
numpy/f2py/tests/src/routines/funcfortranname.f,sha256=ruyXK6eQSLQnQ_rODT1qm1cJvpHrFhI6NRrnWvEIK0U,128
numpy/f2py/tests/src/routines/funcfortranname.pyf,sha256=EgRw8ZWGdd2uK4qCZD89r9VQtEXmnKDx59OpB0K58as,451
numpy/f2py/tests/src/routines/subrout.f,sha256=35DjHIj85ZLkxRxP4bs-WFTQ5y1AyDqBKAXTzSSTAxE,94
numpy/f2py/tests/src/routines/subrout.pyf,sha256=xT_WnDpvpyPb0FMRAVTRRgm3nlfALf1Ojg8x3qZNv_4,332
numpy/f2py/tests/src/size/foo.f90,sha256=nK_767f1TtqVr-dMalNkXmcKbSbLCiabhRkxSDCzLz0,859
numpy/f2py/tests/src/string/char.f90,sha256=X_soOEV8cKsVZefi3iLT7ilHljjvJJ_i9VEHWOt0T9Y,647
numpy/f2py/tests/src/string/fixed_string.f90,sha256=tCN5sA6e7M1ViZtBNvTnO7_efk7BHIjyhFKBoLC3US0,729
numpy/f2py/tests/src/string/gh24008.f,sha256=Z6cq8SFGvmaA72qeH9tu1rP8pYjqm0ONpHn7nGbhoLA,225
numpy/f2py/tests/src/string/gh24662.f90,sha256=xJkiYvrMT9Ipb9Cq7OXl1Ev6TISl8pq1MGemySzfGd0,204
numpy/f2py/tests/src/string/gh25286.f90,sha256=lqEl81Iu9GIDTAbOfkkNGcGgDyyGnPB44mJw2iK1kng,318
numpy/f2py/tests/src/string/gh25286.pyf,sha256=wYkkr5gEN9_RtGjpqh28X1k8KCgh0-Ds9XAt8IC9j4A,393
numpy/f2py/tests/src/string/gh25286_bc.pyf,sha256=ZRvgSzRlaPEx8GyNt97FrRhtCg-r4ZTEDsHNBfit4m8,396
numpy/f2py/tests/src/string/scalar_string.f90,sha256=U1QqVgbF1DbxdFekRjchyDlFRPnXwzG72kuE8A44Za8,185
numpy/f2py/tests/src/string/string.f,sha256=JCwLuH21Ltag5cw_9geIQQJ4Hv_39NqG8Dzbqj1eDKE,260
numpy/f2py/tests/src/value_attrspec/gh21665.f90,sha256=MbbSUQI5Enzq46KWFHRzQbY7q6ZHJH_9NRL-C9i13Wg,199
numpy/f2py/tests/test_abstract_interface.py,sha256=673rVYr6ZsMSb3lumjiqeyK2DjkMLEFrqmpRljYWRes,833
numpy/f2py/tests/test_array_from_pyobj.py,sha256=8W5j1nqbXbUpCsCw3S5mpGfuStvkFCKVdfXi7eU2co4,24379
numpy/f2py/tests/test_assumed_shape.py,sha256=IyqJPGpGVv_RaRCwrko_793jLxJC1495tR9gAbmTlR8,1515
numpy/f2py/tests/test_block_docstring.py,sha256=0Dh1GXlaCg33DmlbhC08MOBMXdpMbk983MQB2hB7XhA,600
numpy/f2py/tests/test_callback.py,sha256=pIloccFF6nJOMwD4yOiDWHFUtU2PfKkrZfWuhqIhBM0,7375
numpy/f2py/tests/test_character.py,sha256=IuV6DQ--Tr-NEAWSxzWzrjDQtVAgXLiV-jfHi_dc5Sc,22544
numpy/f2py/tests/test_common.py,sha256=z1qoOm6HFvLal_cOCPuNn7NVohWjWBcO2v1maVFfRhQ,661
numpy/f2py/tests/test_crackfortran.py,sha256=0xxfF0AbYddVou72KbRZX2IMnSfUh3Cj5hh5FEH5vjM,16801
numpy/f2py/tests/test_data.py,sha256=JSObh8NfZipQQp0_021GLVKhmwhiNxEvAf5Zm2q0dds,2958
numpy/f2py/tests/test_docs.py,sha256=IAauf96ibmpi6hzND8dI_vfAnLoUn-GzHMVf05GIwJM,1909
numpy/f2py/tests/test_f2cmap.py,sha256=2Yy4zuFrkn0QvCkiRjGiHqirp9bXe8ODSnM_LYNAUsM,400
numpy/f2py/tests/test_f2py2e.py,sha256=BeUBmCNKiXYe2TxPMChpHMCo7MZnbqYHl6iToJ4q25g,28832
numpy/f2py/tests/test_isoc.py,sha256=KGUijaN2Qum_qQD1Rc7m7B3dMTx47oRud8ZWNfc5M0Y,1481
numpy/f2py/tests/test_kind.py,sha256=iVs-TL343aNa6NOaw31EaYB3scFdnU4n0_IKPdjyAco,1832
numpy/f2py/tests/test_mixed.py,sha256=95O8xkouDaNFckMa2T4qnUfBpVEVugbM0iruQo9JFpw,893
numpy/f2py/tests/test_modules.py,sha256=mMLzcjENVJ3on--z9qmbUthruWz02T49XiY_A0xbzkw,2380
numpy/f2py/tests/test_parameter.py,sha256=KTmgD77wZFHqZyq4wfRGbR9RisNti8IgO-Q6mUneSwo,4753
numpy/f2py/tests/test_pyf_src.py,sha256=RLm95aANGakQYCzk_UJjUcq0mOQH0LtD6HoZYkEiIrU,1179
numpy/f2py/tests/test_quoted_character.py,sha256=cLPRMhNiCO0v-_A5jPkTg-Zv38U-bbJteuLOL9VSZik,493
numpy/f2py/tests/test_regression.py,sha256=A3a3hbpMqUrFEKp3p3IxueubfaoZyJZBJQz7A0BJqe4,6023
numpy/f2py/tests/test_return_character.py,sha256=9hAUrTWmHkSnRQM4pz43cLFBSEIU5sN8g2M8xaqBqBE,1557
numpy/f2py/tests/test_return_complex.py,sha256=ynSaaMSxiBTApp-tIGwXHLe5gCjqm4qJCq_QNwihGWk,2481
numpy/f2py/tests/test_return_integer.py,sha256=PNeeeykh0Q9oPxUCcuLC3Q1XFbRrk7jhQwK6erjau0M,1830
numpy/f2py/tests/test_return_logical.py,sha256=gPBO6zxmwek0fUIvCDgybiltiNqiMwaIqqsY2o0PXtg,2081
numpy/f2py/tests/test_return_real.py,sha256=e39QqQEDkpxkVEl_5qK67cu7uv0iZUaRA7tlYeKynV0,3354
numpy/f2py/tests/test_routines.py,sha256=4Bg3qLRIyKFzdM3BoRW6vn6CKI2EUzlt5wnHDBzBx0c,822
numpy/f2py/tests/test_semicolon_split.py,sha256=ZkWpV7iKLoSffVdoz_iDdmZnm0Ty4zZSG5git8dsBeY,1700
numpy/f2py/tests/test_size.py,sha256=GKZ5zCsY-wWq4zwlBfMpLub-9Mziy5GFOC7dg39k7ng,1198
numpy/f2py/tests/test_string.py,sha256=KEic6DcDoHZuqofWtytUAqaOC-GWR4SVa2jxsdXq1zw,3034
numpy/f2py/tests/test_symbolic.py,sha256=Zk4h3WC2etMrIEyMrayPpGthpWfuS35Yz-4XzzGFcY4,18835
numpy/f2py/tests/test_value_attrspec.py,sha256=CbcEA3U2rFrFE-7roKIXQXP02Vq7pgwicrP05XrizK0,343
numpy/f2py/tests/util.py,sha256=9BhKV5A5gwhrBBdnI_aa0n5PwPXS4yRijd8VjBzflCA,12615
numpy/f2py/use_rules.py,sha256=zWh8pG5ewfg_LInDmT48O7c0oBlDaGQ4exp5C5ZUZzU,3621
numpy/fft/__init__.py,sha256=MwVEjIo3wDxMAbKERmmX3cHh8EK9nIw9vlUNTpOgNyo,8541
numpy/fft/__init__.pyi,sha256=9LUY_NorLJecQQHN-0dLE9uVLhwv03Bh2iFVTOpSMW8,557
numpy/fft/__pycache__/__init__.cpython-313.pyc,,
numpy/fft/__pycache__/_helper.cpython-313.pyc,,
numpy/fft/__pycache__/_pocketfft.cpython-313.pyc,,
numpy/fft/__pycache__/helper.cpython-313.pyc,,
numpy/fft/_helper.py,sha256=nAtQQ7eHZrQhws3IEIBtpnCWA4emPricOmNnXrm_bng,7010
numpy/fft/_helper.pyi,sha256=Fraw7-4rRa4tl_UT1HWzvGrR2bE7rNcru0PpsC1_byU,1379
numpy/fft/_pocketfft.py,sha256=3M0RsdVo_6SpjG12H7W67Wr5GGXc83ipSAx-4gCV2VY,64379
numpy/fft/_pocketfft.pyi,sha256=PUfhum-xLMNaYacpoJZj3ho-wsoIWrbAS_pq84V8oEc,3292
numpy/fft/_pocketfft_umath.cp313-win_amd64.lib,sha256=geFn0rRssDXbwv5ZGe8yZU-QRkECnvFdKyOao0keYDc,2176
numpy/fft/_pocketfft_umath.cp313-win_amd64.pyd,sha256=UvRBVlR5tnYgCIgoesn0l_lnx2p0RE84G8BI__acPd8,279040
numpy/fft/helper.py,sha256=Dvf6DS9pHTCmugMQy5IBwk5LlSt5PjdShv1IRsUySIY,626
numpy/fft/helper.pyi,sha256=MDJI7k0BFz8N1DuYkyBCEdaT09d3CHEsBaG9JAgs2aI,913
numpy/fft/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/fft/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/fft/tests/__pycache__/test_helper.cpython-313.pyc,,
numpy/fft/tests/__pycache__/test_pocketfft.cpython-313.pyc,,
numpy/fft/tests/test_helper.py,sha256=-CrZvGxoD1xhFNVsHJS3oNTw6yYoNq06CKHmWO_0fSk,6316
numpy/fft/tests/test_pocketfft.py,sha256=QasTw3GPyU-MiB1qgtcDxBSjCGrBnCt0BTUmMjnrAFU,24999
numpy/lib/__init__.py,sha256=pcYU9wc4cOsrPI9GocW4nkAHr28r3OkEFWx6b6tXsdY,3320
numpy/lib/__init__.pyi,sha256=ytClnxgcmYBSM80EuL8ooDJr7uMZttvWzi2JexClhPQ,538
numpy/lib/__pycache__/__init__.cpython-313.pyc,,
numpy/lib/__pycache__/_array_utils_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_arraypad_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_arraysetops_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_arrayterator_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_datasource.cpython-313.pyc,,
numpy/lib/__pycache__/_function_base_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_histograms_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_index_tricks_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_iotools.cpython-313.pyc,,
numpy/lib/__pycache__/_nanfunctions_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_npyio_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_polynomial_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_scimath_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_shape_base_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_stride_tricks_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_twodim_base_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_type_check_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_ufunclike_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_user_array_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_utils_impl.cpython-313.pyc,,
numpy/lib/__pycache__/_version.cpython-313.pyc,,
numpy/lib/__pycache__/array_utils.cpython-313.pyc,,
numpy/lib/__pycache__/format.cpython-313.pyc,,
numpy/lib/__pycache__/introspect.cpython-313.pyc,,
numpy/lib/__pycache__/mixins.cpython-313.pyc,,
numpy/lib/__pycache__/npyio.cpython-313.pyc,,
numpy/lib/__pycache__/recfunctions.cpython-313.pyc,,
numpy/lib/__pycache__/scimath.cpython-313.pyc,,
numpy/lib/__pycache__/stride_tricks.cpython-313.pyc,,
numpy/lib/__pycache__/user_array.cpython-313.pyc,,
numpy/lib/_array_utils_impl.py,sha256=8V5hh2JYzL0LKy2KBrRPh-FZHjfKrn7nyS_VNrvRSO0,1751
numpy/lib/_array_utils_impl.pyi,sha256=PvJJhNRRyOcLtY-FGwkAmRCdJfHEWcl4BwnU11Waq_s,818
numpy/lib/_arraypad_impl.py,sha256=Ri6I_s95XLEUwSykx9eOBz7JqiPNKQTMH24uQrDBD-k,33217
numpy/lib/_arraypad_impl.pyi,sha256=bOjBqunPqcmMN4RiOMe0T5d03gIMxIm_fkGMn8ONiSM,1881
numpy/lib/_arraysetops_impl.py,sha256=4_42VA7Lu4P-ODK9M3kusK-xKQP5u7tcThyuHMcn0Vo,40524
numpy/lib/_arraysetops_impl.pyi,sha256=B8kt91kSzkrtA-fwqwWWLNpPDzEmcM0-BWKiYd9Ne0I,9948
numpy/lib/_arrayterator_impl.py,sha256=n1_emvfqXcvOtLhaeMtCvYiZqBCnVCNfwpI95loCBa0,7410
numpy/lib/_arrayterator_impl.pyi,sha256=U-olocb1ETuXURoG6QuYhq1xoeDxrtSQInphHsHCGiU,1873
numpy/lib/_datasource.py,sha256=H7HKFHCye9r2mLDup6KYnuKcjUrOd3Gc2wFMn78rVGY,23429
numpy/lib/_datasource.pyi,sha256=a_mEw94cyK-Ik7ZaQIDIJp8CB2pYV-1FEvRkZHCM20c,1027
numpy/lib/_function_base_impl.py,sha256=3T1Z1wJatBn8fV7okN9moLoiL5jSiNMGRvex0jRfhvg,201865
numpy/lib/_function_base_impl.pyi,sha256=1FuQSPYALot4Z8LXSVvcjiP8ZlZh0fvFBSPHcGgK4iY,23122
numpy/lib/_histograms_impl.py,sha256=WCi3-3v3J6NeXxoQDy2MBxJCQSufgMkmtvr4IIgK-I4,39852
numpy/lib/_histograms_impl.pyi,sha256=HFQ2VaV5St1hH7CrdnUOn2pKurKjXoNRN4TJ9D1vmYI,1118
numpy/lib/_index_tricks_impl.py,sha256=34-lqDXxLap3V6I_C27OeyYJLi1fKIRyz-WJ3K2g7D8,33248
numpy/lib/_index_tricks_impl.pyi,sha256=shCCeeX_Xd_fA18YRmhhtk4WU76BP1qFYOQPB2mzeTg,6521
numpy/lib/_iotools.py,sha256=gfw4LwMszW5bDH78mM1Y_VWWCZ_u24uW-tuoZAVBM2k,31840
numpy/lib/_iotools.pyi,sha256=_234y4IuHkYOY1Mt-7lJ-kJ3FJbhjoMRl0zZhyVS-dk,3493
numpy/lib/_nanfunctions_impl.py,sha256=32gsaYfFPKAqeqxkjl9XAFYaKun6F9f9-Auhk0t73Jk,74178
numpy/lib/_nanfunctions_impl.pyi,sha256=N8tDZ0fhyMiFD0-4M_rBP61DmDkIGiN171GNjwVxlck,886
numpy/lib/_npyio_impl.py,sha256=53vlumSVsWMjaSaFNtM7Adb8xzBlEI1KiZB-Yxiuvx0,101972
numpy/lib/_npyio_impl.pyi,sha256=YH-oiyiwd5li4imo6_kjvpxGgnO-_wQuD4NFsz0ClW0,9555
numpy/lib/_polynomial_impl.py,sha256=o5Qd-VSe36gsgEuJBrjJrW4j5ZMMfYuq6howHR3TazE,45752
numpy/lib/_polynomial_impl.pyi,sha256=MmlWawTNlEnyvlMokm_w3IyWpUIDQvPjixH3CGVurMo,7432
numpy/lib/_scimath_impl.py,sha256=3P2GH0hldWMw6hgqNmv_KXlENfFYzeIy9gqrqcOugdg,16334
numpy/lib/_scimath_impl.pyi,sha256=9aJJX_D19ccIrGqy0VCmRGAGVoluWCYQLW7_ecyk_XE,3049
numpy/lib/_shape_base_impl.py,sha256=LgKYMnNErkMy8xbIumjaPJHFPOALvFTRwGQHYjfCtZo,40647
numpy/lib/_shape_base_impl.pyi,sha256=fl0aTfotN-9IOeGsEAVLnexbM-njPFKnYU13cBujHkE,5513
numpy/lib/_stride_tricks_impl.py,sha256=0Lrnvmponu10hS2g6E0Ec7sHuNrfNS5CoPZPqWPP74M,18574
numpy/lib/_stride_tricks_impl.pyi,sha256=FeWPs1yD4uQQSis8w4cm9-YW7IQ6bv333JFaqIc0zrQ,1881
numpy/lib/_twodim_base_impl.py,sha256=_pfbE4LTkMSssA5Piz1F2c9pOMtJMO7LNw_My6PF0kA,35052
numpy/lib/_twodim_base_impl.pyi,sha256=JgqPjlBo1JFeYdMnb2NkMYyR8Dvami3hjk4VQ-MK6mY,11706
numpy/lib/_type_check_impl.py,sha256=NZhF_zIbdmDzbLbwakOnPhl2eRz3lJW_rNzBCULLSEk,19919
numpy/lib/_type_check_impl.pyi,sha256=si8-6dnzrcaeVtdqnoL6Cu82ZdoB9inkjmZxiRdaFz4,5366
numpy/lib/_ufunclike_impl.py,sha256=mq924a_rI7wvsWoPKHyc38WLI11fxCAiog-k6gJ5br0,6516
numpy/lib/_ufunclike_impl.pyi,sha256=4Q_uMOYYI58InUsBcBnq3l-JBROsvEUytTB7xYBT8ls,1389
numpy/lib/_user_array_impl.py,sha256=mx1xZjZib3SxnopvslFEK6Z-ql_ZzgUsn1u0LZ8KnXw,8262
numpy/lib/_user_array_impl.pyi,sha256=no_xh1L4-mCrXwMRcYGVEqRsznuuYq8kwgkRcaHuWkc,9521
numpy/lib/_utils_impl.py,sha256=9jwNKayFoYxCrg4GgdFXBCZwfjMNczoaQBfs0msKxVs,24163
numpy/lib/_utils_impl.pyi,sha256=Avu_JgLOX4FJnFI8KcqUWdx_V2ldS6_YHNMg9yvfygY,284
numpy/lib/_version.py,sha256=m4Z1ufCoQH5yYndKrkXKiN3p8FIygUbeYt2fjGfi2Rs,5009
numpy/lib/_version.pyi,sha256=zAmfNnFeke7_lHsvR94fafNBcuJHpZ1jaB2PyzEostc,658
numpy/lib/array_utils.py,sha256=SyMHXlsOJMKwxkjQxjsxx3J2cgx_3J2N0qqmLZTQgMc,137
numpy/lib/array_utils.pyi,sha256=YYnx_V4CMdSbJTCnYboN1swcswmlOD2e4ZvQj5WsSak,197
numpy/lib/format.py,sha256=yKvqaH4nwrS7GPoQUV_YgnNC_s0KQaZ-08WG10q1x3I,37208
numpy/lib/format.pyi,sha256=qF5MgX4HL45SWz12KobX03cr40MoDiXDs4vFltAZVuE,770
numpy/lib/introspect.py,sha256=P7-Um4--wGHOWLVusNN1bhjMuA1g6kKmu-jx1GGeOPM,2810
numpy/lib/introspect.pyi,sha256=IsntuFrlFhRBZcGGhRUTAgnONUHEbYw_2ApPmffx8QE,155
numpy/lib/mixins.py,sha256=hSDMCuYP518waugn3Vdu_S4tbXtDeUkc-zB3wwzyoOI,7519
numpy/lib/mixins.pyi,sha256=pBHGtj8_EFCwyv6uPlKEMrBTOysEezePNdjqUYMsgPM,3205
numpy/lib/npyio.py,sha256=nZadg1IKRXTLZX_52TpjU-YutNH5QA_UU457rHfn6oc,65
numpy/lib/npyio.pyi,sha256=6xZ6zF-6qKuSOfjjDL4YN43xKPYcD6IpzJiDiLpmSSs,121
numpy/lib/recfunctions.py,sha256=SgWulquccYhucyVpz1aU6Qi8pbcMKyEppbsrpIsh-nM,61339
numpy/lib/recfunctions.pyi,sha256=Hjbbqt7Jl-bmrWEBvR1ZZHL6EForacXyk73oRnlBXus,13718
numpy/lib/scimath.py,sha256=HgFt3iWrgcxgV4Y6U-xyZZBM_MMewX62uP8HhOxhveY,122
numpy/lib/scimath.pyi,sha256=PhlpjveDqnSQvLn2cQ1AQFNVpxECaBWgYvhK8S32jzo,245
numpy/lib/stride_tricks.py,sha256=BDqFklWQ4eVAoAvtdb_3nT0YxXeMZOtPp6nBr7gKG64,85
numpy/lib/stride_tricks.pyi,sha256=6-K3R7XBw_fcpHaAIs9y4LEc5i4r5gZUG-tg4EOR-ew,128
numpy/lib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/lib/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test__datasource.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test__iotools.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test__version.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_array_utils.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_arraypad.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_arraysetops.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_arrayterator.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_format.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_function_base.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_histograms.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_index_tricks.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_io.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_loadtxt.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_mixins.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_nanfunctions.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_packbits.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_polynomial.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_recfunctions.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_shape_base.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_stride_tricks.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_twodim_base.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_type_check.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_ufunclike.cpython-313.pyc,,
numpy/lib/tests/__pycache__/test_utils.cpython-313.pyc,,
numpy/lib/tests/data/py2-np0-objarr.npy,sha256=ZLoI7K3iQpXDkuoDF1Ymyc6Jbw4JngbQKC9grauVRsk,258
numpy/lib/tests/data/py2-objarr.npy,sha256=F4cyUC-_TB9QSFLAo2c7c44rC6NUYIgrfGx9PqWPSKk,258
numpy/lib/tests/data/py2-objarr.npz,sha256=xo13HBT0FbFZ2qvZz0LWGDb3SuQASSaXh7rKfVcJjx4,366
numpy/lib/tests/data/py3-objarr.npy,sha256=7mtikKlHXp4unZhM8eBot8Cknlx1BofJdd73Np2PW8o,325
numpy/lib/tests/data/py3-objarr.npz,sha256=vVRl9_NZ7_q-hjduUr8YWnzRy8ESNlmvMPlaSSC69fk,453
numpy/lib/tests/data/python3.npy,sha256=X0ad3hAaLGXig9LtSHAo-BgOvLlFfPYMnZuVIxRmj-0,96
numpy/lib/tests/data/win64python2.npy,sha256=agOcgHVYFJrV-nrRJDbGnUnF4ZTPYXuSeF-Mtg7GMpc,96
numpy/lib/tests/test__datasource.py,sha256=H6PZKQ0tY6r1bhrcLRKMjWdWop5P4Rj_SYvrU9ukDzc,10921
numpy/lib/tests/test__iotools.py,sha256=ejbG7SVvTm55Lq5LdUza8-nIvF2mt-XYvfpzn13q038,14097
numpy/lib/tests/test__version.py,sha256=v2TOlH4f1Pmzxn1HWby3eBgLO9tGnhwH2LvBXlXtHP4,2063
numpy/lib/tests/test_array_utils.py,sha256=Fy8_PR6GHed-mStqcbfjTe8Q5zMZnJ9WzFzX6DjoRR0,1152
numpy/lib/tests/test_arraypad.py,sha256=Nc4xoxjlZkuaFSWgc2uP9bIXiLaYcje1tFF2fbIMlAE,57480
numpy/lib/tests/test_arraysetops.py,sha256=yQy2uGGx_oYJu8nDEYujP_NIlDBmxCiyH5a7t5UH8cA,39023
numpy/lib/tests/test_arrayterator.py,sha256=IRVmzxbr9idboJjOHKuX_8NQhMAKs7pD1xWqmU3ZERw,1337
numpy/lib/tests/test_format.py,sha256=6Kt8l-P9lYsCbY2KRQBusCrNeoGYlFTMcFagXs3gVeY,41937
numpy/lib/tests/test_function_base.py,sha256=7jlXXT_0-ChV3Sak8bjxHGyfoXcramgCi6MTDIryaaw,173318
numpy/lib/tests/test_histograms.py,sha256=4PnaePQSpV_HsKynnbe5Hc5L02Z66ecCL24cvAYoeRg,34535
numpy/lib/tests/test_index_tricks.py,sha256=tgXpLGpT9XpO_djXCTKpM0-WF-AVE5GF8lbvIyUz9X4,20921
numpy/lib/tests/test_io.py,sha256=cUYVHDew1N-OfetUt-8e19VqMMWAUKZNhN6udbxfcZw,112868
numpy/lib/tests/test_loadtxt.py,sha256=pvRZMon6Vyy_pdbEiJdJi17RW6I-Rg83Uc3XwMCvew0,41622
numpy/lib/tests/test_mixins.py,sha256=nIec_DZIDx7ONnlpq_Y2TLkIULAPvQ7LPqtMwEHuV4U,7246
numpy/lib/tests/test_nanfunctions.py,sha256=oeuoa1r3zx5JJFkU_zdne8GMSWET3UPDxL1sdVZPfAM,54762
numpy/lib/tests/test_packbits.py,sha256=yN8rYbPDteOPJf7dEeZkgSnyzIUKe_ituLYdsqxcqbQ,17920
numpy/lib/tests/test_polynomial.py,sha256=qYZGXUIeyZoNjbkEYeuUq1ad2eCPDkNXj6MxakvbIvk,11731
numpy/lib/tests/test_recfunctions.py,sha256=Yg2pQEcOgf4d3PgrGiwxhrrXfyPXRa3SsyItxkY_wwA,45029
numpy/lib/tests/test_regression.py,sha256=aCW5aT1PJL1ZCwrHUSa7iixQLWMC3D5iFSRDsWE2Uag,7921
numpy/lib/tests/test_shape_base.py,sha256=fYKyGdLTM-l2rlTHAzDJbObc_SQWXXF8QoKt266F7K4,28296
numpy/lib/tests/test_stride_tricks.py,sha256=EKHYiPoawG_vu_tFmKi5Lmvfs0VEDcUW7feiWybUGXA,23644
numpy/lib/tests/test_twodim_base.py,sha256=mNNXsDKT3hPpz-HB_1k8YTWpwdx7dnvmrWWS_Lkew30,19382
numpy/lib/tests/test_type_check.py,sha256=2lnLRzUA0voTKURi-qXllYYxBAqpsVAJmMtLQCHoIYA,15145
numpy/lib/tests/test_ufunclike.py,sha256=9C9LV3XZLaHNQoyRVZl-C4w9HcOTEJMDw2uXYXhf1u4,3123
numpy/lib/tests/test_utils.py,sha256=KN1q-eFLmckYbOMTxPKTwFMPtzBHdAPb0j9ntfea_yM,2454
numpy/lib/user_array.py,sha256=v3dCCNs-PZ7tHZ1vqGqdeV5FLHRiLLWrMZhdzQTSRAM,50
numpy/lib/user_array.pyi,sha256=IaCNerLboKjt3Fm-_k_d8IqeyJf7Lc9Pr5ROUr6wleM,54
numpy/linalg/__init__.py,sha256=AZnH2FnMk_bDy8VuOsihmoS-nICrpKIRMPNa5Puyk30,2201
numpy/linalg/__init__.pyi,sha256=Czr1hGuEjSGY_J7NbFaprCisxeIANCZAYqKz0YRUQAI,1076
numpy/linalg/__pycache__/__init__.cpython-313.pyc,,
numpy/linalg/__pycache__/_linalg.cpython-313.pyc,,
numpy/linalg/__pycache__/linalg.cpython-313.pyc,,
numpy/linalg/_linalg.py,sha256=yqAqD7BFR_C8y1I4BJ9KAiYkmhUJ1683g5hQoxvYjl4,118309
numpy/linalg/_linalg.pyi,sha256=namoF69OjkhE0tz2ngdjcdU0NwPG4uhoDLjzb5_iAnc,11867
numpy/linalg/_umath_linalg.cp313-win_amd64.lib,sha256=_qdjTm97jJF7CFFZ19lve5GMu6bu0BNP90I6YBeLu1Y,2120
numpy/linalg/_umath_linalg.cp313-win_amd64.pyd,sha256=Et9V81Nm3g8ittDrISaxwevTK1ZEaP8tQRzlYfV_Sc4,108032
numpy/linalg/_umath_linalg.pyi,sha256=g5NJoNte6CwuMFDfd55O8OvJv4lOi539VKAB-Mrc864,1470
numpy/linalg/lapack_lite.cp313-win_amd64.lib,sha256=G9-WJipIoEv4P-pQa_zNSlN0zlR56e-GArTXpz4SO3Q,2084
numpy/linalg/lapack_lite.cp313-win_amd64.pyd,sha256=qMEWgGVLc_XuvKeZKY9uPtnD2kkP5WRlPR42vdjCoKI,17920
numpy/linalg/lapack_lite.pyi,sha256=sWKWBDR2UP0ez6ETdE0Rz-mp8m_gOCMo4CYVZajDMNo,2818
numpy/linalg/linalg.py,sha256=1CC9jc-u61GePC5AuieDiyMyrVvgLD8ZJbTPvLfKjHc,600
numpy/linalg/linalg.pyi,sha256=iGd8b4-gN1d92K7wfgDZxoHrVXnVC1c6vGqW4ZbWldY,1001
numpy/linalg/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/linalg/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/linalg/tests/__pycache__/test_deprecations.cpython-313.pyc,,
numpy/linalg/tests/__pycache__/test_linalg.cpython-313.pyc,,
numpy/linalg/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/linalg/tests/test_deprecations.py,sha256=GaeE3JnQlJLoAfbY93LmgCFUlV5M8IFmQ7EhF4WbqwU,660
numpy/linalg/tests/test_linalg.py,sha256=D7AhxqGiHwPjIHI3JNkPcsYFOstTiKe_PTNl6d4YlSE,85701
numpy/linalg/tests/test_regression.py,sha256=KRXOhAHjZbE3h6vqksTvQayrvhUkyRM8_O6Ky5s-Nqs,6866
numpy/ma/API_CHANGES.txt,sha256=U39zA87aM_OIJhEKvHgL1RY1lhMJZc1Yj3DGLwbPbF0,3540
numpy/ma/LICENSE,sha256=1427IIuA2StNMz5BpLquUNEkRPRuUxmfp3Jqkd5uLac,1616
numpy/ma/README.rst,sha256=_MHrqHTE8L4wiJJqvaOh1l-xTxidwdilc_SZkFbgubM,10110
numpy/ma/__init__.py,sha256=EFe3qk5iN_7Z__BwlkEW6xo2Zc6NnI8F7G2b1UVW4uY,1473
numpy/ma/__init__.pyi,sha256=76dORzdLey4HoMD26xJFuw-2aIGrB30xt8rFqE8xafY,7404
numpy/ma/__pycache__/__init__.cpython-313.pyc,,
numpy/ma/__pycache__/core.cpython-313.pyc,,
numpy/ma/__pycache__/extras.cpython-313.pyc,,
numpy/ma/__pycache__/mrecords.cpython-313.pyc,,
numpy/ma/__pycache__/testutils.cpython-313.pyc,,
numpy/ma/__pycache__/timer_comparison.cpython-313.pyc,,
numpy/ma/core.py,sha256=4Jv1_64eM6_aBIQ3sp4uKUU8sfxslMzeGB1AoyTTico,299477
numpy/ma/core.pyi,sha256=2akub4Nv5xHCrhtBQ28pVG2BcnU8hi65gK-UkZP0_6o,18835
numpy/ma/extras.py,sha256=0Od0rMKh6FLyG0byaU5kAeWcZCRfcVQRTNutMfmiCRo,72951
numpy/ma/extras.pyi,sha256=YYuESxQTbtdLwxk_rZz7oZbg_JJMzBo92CEpFIpCWnA,3938
numpy/ma/mrecords.py,sha256=BXglbMRYLeB5FxBcU_1vzmMZpF6iXaKS57qpTkWFm8A,27888
numpy/ma/mrecords.pyi,sha256=oGSsEingxJ_A07fLNDrckjS7MwA8yZN_N6wkOEmRPeE,2078
numpy/ma/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/ma/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_arrayobject.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_core.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_deprecations.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_extras.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_mrecords.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_old_ma.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/ma/tests/__pycache__/test_subclassing.cpython-313.pyc,,
numpy/ma/tests/test_arrayobject.py,sha256=ap06C0a0dGWcOknpctbhLbzHSNd2M9p_JL2jESqBBGk,1139
numpy/ma/tests/test_core.py,sha256=UfmlFEHJCksx4ad9UsP77n9bOenWqqe4peM7G5JG18k,225055
numpy/ma/tests/test_deprecations.py,sha256=WurKSuN6hsXmWxRoxstdVBXcKCTvYxlYz-ntSkW6qKc,2650
numpy/ma/tests/test_extras.py,sha256=C_auxUGRJ38o-7LZGNTN5IdAi48c1QIY8bzM2NozB6g,80274
numpy/ma/tests/test_mrecords.py,sha256=TzQwlvY1iJnKH7ARsOI9nNaNeTt1sGgZAj8NEjP7jY0,20348
numpy/ma/tests/test_old_ma.py,sha256=tQ-IqKZ1NMHq5_8qkOaZWg_rZkWBpRaPnlodBRd_ABA,33629
numpy/ma/tests/test_regression.py,sha256=J1ftHDKfIF3SUIgQlxJplCsYTrPpAyN4rf5K1Uw5T8w,3384
numpy/ma/tests/test_subclassing.py,sha256=UFK0R44pRCmcENP2kbI_4hRMQ7YC6qjplZNM0WeqcCM,17469
numpy/ma/testutils.py,sha256=86e8bckl-C24JBICXzVMI_s4RqtbgZqDLD0L5tZPTgc,10564
numpy/ma/timer_comparison.py,sha256=a3kW2PlSCDXmVrDx0VGPQ9vhcQIuDUPEnKZ54zVP810,16153
numpy/matlib.py,sha256=DJsayODBbd0n6MmhxPmgiL28ALyLgQdHtQ5BHKggY5I,11036
numpy/matlib.pyi,sha256=Is_0Dii3OSM58bzPXiiJV46xGUK9Nb34adHOqNlMbME,10214
numpy/matrixlib/__init__.py,sha256=9-DMlmdLxOk5HSGJ20AuTjKkGZ3MUPHCFjhE6sb4NMo,253
numpy/matrixlib/__init__.pyi,sha256=ZAutkmA8BpttneOyZNqAjiYJN5F7sl-WSAfIkkI2qlI,109
numpy/matrixlib/__pycache__/__init__.cpython-313.pyc,,
numpy/matrixlib/__pycache__/defmatrix.cpython-313.pyc,,
numpy/matrixlib/defmatrix.py,sha256=jMYex3MhNKlvjgcP9EAYMt_yrVQ0O67ZuXwMRvFCff0,31918
numpy/matrixlib/defmatrix.pyi,sha256=a4tKYShYR9EeU9Ftub3_iyGzwunUIqK_6uz_B0ZAyXQ,495
numpy/matrixlib/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/matrixlib/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_defmatrix.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_interaction.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_masked_matrix.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_matrix_linalg.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_multiarray.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_numeric.cpython-313.pyc,,
numpy/matrixlib/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/matrixlib/tests/test_defmatrix.py,sha256=3cSTjFilFZVq2fMgfoUlx6hf9N4MSvBMhHcemoiUzLA,15488
numpy/matrixlib/tests/test_interaction.py,sha256=9loMwSKXBOu09Z6aZ6_RG7ojbEfn19A8N39h12F5668,12249
numpy/matrixlib/tests/test_masked_matrix.py,sha256=SjuUs4IhE3x2y8oM9uoWhKX4K1sX2JNkLQMlhMlvzD0,9146
numpy/matrixlib/tests/test_matrix_linalg.py,sha256=9S9Zrk8PMLfEEo9wBx5LyrV_TbXhI6r-Hc5t594lQFY,2152
numpy/matrixlib/tests/test_multiarray.py,sha256=E5jvWX9ypWYNHH7iqAW3xz3tMrEV-oNgjN3_oPzZzws,570
numpy/matrixlib/tests/test_numeric.py,sha256=l-LFBKPoP3_O1iea23MmaACBLx_tSSdPcUBBRTiTbzk,458
numpy/matrixlib/tests/test_regression.py,sha256=wpWVjM4pHRaiVX_Y5_zc6yNr4I5zWdmJfHTwbmBUhew,963
numpy/polynomial/__init__.py,sha256=JAnPIGbR7QJilyIhHjVvA7SsWGSO1Sm0PCse-XWk3dY,6947
numpy/polynomial/__init__.pyi,sha256=885H4pfwJHj0xFuPDsV6p_ON2nJenGjA5h8d4uMY-IY,711
numpy/polynomial/__pycache__/__init__.cpython-313.pyc,,
numpy/polynomial/__pycache__/_polybase.cpython-313.pyc,,
numpy/polynomial/__pycache__/chebyshev.cpython-313.pyc,,
numpy/polynomial/__pycache__/hermite.cpython-313.pyc,,
numpy/polynomial/__pycache__/hermite_e.cpython-313.pyc,,
numpy/polynomial/__pycache__/laguerre.cpython-313.pyc,,
numpy/polynomial/__pycache__/legendre.cpython-313.pyc,,
numpy/polynomial/__pycache__/polynomial.cpython-313.pyc,,
numpy/polynomial/__pycache__/polyutils.cpython-313.pyc,,
numpy/polynomial/_polybase.py,sha256=SsFFCPQxtXzxDgXMsD2ovvoBL-1jQIrmdWCUMBizyPs,40648
numpy/polynomial/_polybase.pyi,sha256=Kt1x4PgzInVS9mMR_C5d6yjJaIPcfMyhp0tp0Bz2FZk,8821
numpy/polynomial/_polytypes.pyi,sha256=-NjNhcMP9dwCdWrIod0uRJmSNtqIQSQ6lSbvSy3aKd4,23455
numpy/polynomial/chebyshev.py,sha256=f0h4dyuTy1KePOjKo7tBeYmLvrh1YcFBzi3i5wZyg1w,64168
numpy/polynomial/chebyshev.pyi,sha256=AnJkNZoHyIUQvFbQfexdey-GJwN3fMjZs2pDZT6YzvQ,4917
numpy/polynomial/hermite.py,sha256=XlsIKUW1sAGtdUqUpzZOt9BPyyDebQl_fK7zrlZI8GI,56206
numpy/polynomial/hermite.pyi,sha256=xggYYL_74IGVlqmK9NXXIiSpGKELIcoqaOOJ0enXvPU,2551
numpy/polynomial/hermite_e.py,sha256=QBvJfj8aOxTq4qFpY2Fjo0EZs5AEhd_ur4pIh5dq3XA,53850
numpy/polynomial/hermite_e.pyi,sha256=CGq8MpTXOonV1JzfLdWuN_-pXOYEJG4qvNd977s11ho,2643
numpy/polynomial/laguerre.py,sha256=ITXPSdc15HORhN5stSri5hGZyuCvv6ZxD2lYLMosSqQ,54054
numpy/polynomial/laguerre.pyi,sha256=ftBF2ZU4CFriNY6xy8lGP-gNxRB4udAI4HVW7nkv2R0,2274
numpy/polynomial/legendre.py,sha256=8WMBxMF_AQtfa4d46JYnQCYnbMFBTsixpVm-iBe5iDk,52599
numpy/polynomial/legendre.pyi,sha256=590XJNm9Yl_ShYBZdcrlB65qs9DEh7OOAmeC_IXu5to,2272
numpy/polynomial/polynomial.py,sha256=N1O1iPZeg15LQTg7W8Qcz4-J7EwDzHlhRKLFXsN10Aw,53819
numpy/polynomial/polynomial.pyi,sha256=0KSIDRCJg1EnrZCuyQVCEKP07IiHTFHyaKPC4Po3jJI,2089
numpy/polynomial/polyutils.py,sha256=wfNdfDePXKCqJIk8VSWjmApQN1TKpCe-YuBurYwJbi8,23287
numpy/polynomial/polyutils.pyi,sha256=zA5UdU71NWqiKv3nAYAt5MAcJgAywHOj9lwjX8sbEro,10857
numpy/polynomial/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/polynomial/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_chebyshev.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_classes.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_hermite_e.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_laguerre.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_legendre.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_polynomial.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_polyutils.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_printing.cpython-313.pyc,,
numpy/polynomial/tests/__pycache__/test_symbol.cpython-313.pyc,,
numpy/polynomial/tests/test_chebyshev.py,sha256=PI2XwvGGqQKEB1RxbsYRgeTG0cunB_8Otd9SBJozq-8,21141
numpy/polynomial/tests/test_classes.py,sha256=VCcG2ICOteBolQHyfzYzMUhyqHlbAJxV8LdQm9NO50U,19057
numpy/polynomial/tests/test_hermite.py,sha256=zHGmy1UAuKtLj5Key6BMne7ZRh3tZpowfleghQzyhqo,19131
numpy/polynomial/tests/test_hermite_e.py,sha256=5ZBtGi2gkeldYVSh8xlQOLUDW6fcT4YdZiTrB6AaGJU,19467
numpy/polynomial/tests/test_laguerre.py,sha256=Bm5SAKjOcQ6RlSsc8SRXYfU34mbdQ2fdMjf2E9ppznM,18047
numpy/polynomial/tests/test_legendre.py,sha256=Vbye67yIzN7Ij2UwYZlhSt68hoNeukFHYd1QCvA70ZY,19240
numpy/polynomial/tests/test_polynomial.py,sha256=zuJJoVLls3H2wnYeLjc514oBCx8hE5AvnbBgtQqJIzI,22660
numpy/polynomial/tests/test_polyutils.py,sha256=b3vdtJVjC34AmEv96sw2IvIABNDqmYhCnMYZCvhtWzU,3897
numpy/polynomial/tests/test_printing.py,sha256=_RIcZxPEUJUb8aSpdAkvnZBwBDfIyR8tKI2--w9Y64o,21854
numpy/polynomial/tests/test_symbol.py,sha256=GZnqB4PLjZDWalREVOAI3qus9kjUDhCW-WZ_87jRmPY,5588
numpy/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/LICENSE.md,sha256=tLwvT6HJV3jx7T3Y8UcGvs45lHW5ePnzS1081yUhtIo,3582
numpy/random/__init__.pxd,sha256=g3EaMi3yfmnqT-KEWj0cp6SWIxVN9ChFjEYXGOfOifE,445
numpy/random/__init__.py,sha256=W_hFzGsKVQfdh3-U15gzsOKKAk8uZgioDkxKyuou4WA,7721
numpy/random/__init__.pyi,sha256=tb8imrQCSdpOL9DxD9WTBEz_Buot9aycQ4YvUr3snhM,2284
numpy/random/__pycache__/__init__.cpython-313.pyc,,
numpy/random/__pycache__/_pickle.cpython-313.pyc,,
numpy/random/_bounded_integers.cp313-win_amd64.lib,sha256=uMpX0ItaqNaKImMCUWz_cxY_gClTnftNOgZBxZqWv5o,18000
numpy/random/_bounded_integers.cp313-win_amd64.pyd,sha256=u5Gu2xAd0Kl6vTv6WlasvePK2W6C8AZMKOYeq_LZBCo,224768
numpy/random/_bounded_integers.pxd,sha256=EOKKUlF9bh0CLNEP8TzXzX4w_xV5kivr1Putfdf6yvU,1763
numpy/random/_common.cp313-win_amd64.lib,sha256=FhxgAP-4i_aqWz_06IA6y1hE6_D5hBUWNBORKpD4ttg,2012
numpy/random/_common.cp313-win_amd64.pyd,sha256=zubEfAnCwBTU37Rc9Sds-SovWpwFvrTUvRDcR1BSRtY,168448
numpy/random/_common.pxd,sha256=2_9NLWFSnLG4iDd-KeYUBRa47QM8qceUsPiAkyWZ74I,5089
numpy/random/_examples/cffi/__pycache__/extending.cpython-313.pyc,,
numpy/random/_examples/cffi/__pycache__/parse.cpython-313.pyc,,
numpy/random/_examples/cffi/extending.py,sha256=BgydYEYBb6hDghMF-KQFVc8ssUU1F5Dg-3GyeilT3Vg,920
numpy/random/_examples/cffi/parse.py,sha256=eRBbVrnxvw0v3BS6JJvX1rjpm1MA7yZu-31QHMuNlp4,1805
numpy/random/_examples/cython/extending.pyx,sha256=1lkq6zFifnwaMtAkVG0i_9SbMiNqplvqnHaqUpxqNzs,2344
numpy/random/_examples/cython/extending_distributions.pyx,sha256=myr53bzJ2kVTltZx_MDcw3Q6bbh1MK1U22GKyaEi5C8,3963
numpy/random/_examples/cython/meson.build,sha256=q_IFcVs_qzERJD_-8uaDnjps3QdaW49okZMbFtwkAPo,1747
numpy/random/_examples/numba/__pycache__/extending.cpython-313.pyc,,
numpy/random/_examples/numba/__pycache__/extending_distributions.cpython-313.pyc,,
numpy/random/_examples/numba/extending.py,sha256=vnqUqQRvlAI-3VYDzIxSQDlb-smBAyj8fA1-M2IrOQw,2041
numpy/random/_examples/numba/extending_distributions.py,sha256=-aTxLIqnXW0XPtmEp0yJfaBTBcjEo9Q9SebKG_dOLvw,2103
numpy/random/_generator.cp313-win_amd64.lib,sha256=9rl_OsTUjUfOt7fEpglT-o9R9iwYnaPNd-s267yg7P8,18400
numpy/random/_generator.cp313-win_amd64.pyd,sha256=Ei7DqRtLrf8yjEWeA3rdR35PSrLYIeeaHerthiKF0lo,748544
numpy/random/_generator.pyi,sha256=flo5onsP6p2SECRZg08N3Ix9JWrcWEtqLzM0JGJQf0o,24865
numpy/random/_mt19937.cp313-win_amd64.lib,sha256=TKv1Ku9Wj_WU9t8VGOWm-F7UKOLvvvvPPL8P5WQzNrw,2032
numpy/random/_mt19937.cp313-win_amd64.pyd,sha256=l7Kzq_YiuHcJs_4uSdDD3EQLZeB0OjBzYjQ1Ak68oCE,89088
numpy/random/_mt19937.pyi,sha256=QB8vx8f-EGl-qz3iYGArFsfPb3Mgqldk128UeWX3kLs,800
numpy/random/_pcg64.cp313-win_amd64.lib,sha256=3C4EpAGYXy2Zc-7jF9bdx0IGB1d5hFaEtZrvFvZ21uk,1996
numpy/random/_pcg64.cp313-win_amd64.pyd,sha256=ffX8s0tG_204_mhfuVaHQGsgLsiClk1URlDNbmjj2TQ,95232
numpy/random/_pcg64.pyi,sha256=TSID_lsjoPvfGIR4cbvGLg41VmbsHclheSt8pfBZPhs,1186
numpy/random/_philox.cp313-win_amd64.lib,sha256=7Mk1BhUFzwXz8kR4UU_86OZvqzCPCkqEYN9HLFRN_Z8,2012
numpy/random/_philox.cp313-win_amd64.pyd,sha256=lgJTensdbXddxDKdfSpr3JRq6Ca7gz71Z5qd5WfMYGE,80384
numpy/random/_philox.pyi,sha256=e7J93SwcbYrDfBfJgnuVIngiEn7NSN7k576J9pz4d54,1044
numpy/random/_pickle.py,sha256=D5MrszR_oDD4yss3bt94MPw54FNIyH6f4MtOWBYRDvk,2832
numpy/random/_pickle.pyi,sha256=V4UAI1td1JPMHeNMZjon30x7E7SD3WZBALC8HzQFciU,1651
numpy/random/_sfc64.cp313-win_amd64.lib,sha256=aKitBgL7vTu4-N6i6UCVxLiNlaRUVzDEHi_GVIEpbnY,1996
numpy/random/_sfc64.cp313-win_amd64.pyd,sha256=BmcHxwy_0F9s4bw99xxnD2oFtgAXd93FmD0j6wTPr2Q,59904
numpy/random/_sfc64.pyi,sha256=HCCIxo0H1b0_s5MEWrwttlElWEE5GKt5wV6LYxIvSxM,710
numpy/random/bit_generator.cp313-win_amd64.lib,sha256=7l1Cu4aNYB89vqLj56eD4uUK-S-jCpPyhRB40Gg3PX4,2120
numpy/random/bit_generator.cp313-win_amd64.pyd,sha256=AjVuSV2Sp54bT_darrtPk42FWdrzZD9GdIknwoHzGhI,171008
numpy/random/bit_generator.pxd,sha256=LJpeB-EKeVV8_JO69sS33XJLZQ3DAhrUCNzs_ei7AoI,1042
numpy/random/bit_generator.pyi,sha256=OqHUYtl94gRrT5AV-A7iV-0QKumGfmb3jrkCoUkI4Xc,3641
numpy/random/c_distributions.pxd,sha256=02WeqbzQ4heQ1cZ7ShePejxmt5AOI5kTstBZ5w2WxD0,6454
numpy/random/lib/npyrandom.lib,sha256=QSx4TSq4CcooYh9T_Qln9CMT9D0eixDKyokEiHATEoQ,148178
numpy/random/mtrand.cp313-win_amd64.lib,sha256=UTiwKIE9oF5UlrLFIYmTBftFvFCbE_6Msdd9bjCRcHg,17122
numpy/random/mtrand.cp313-win_amd64.pyd,sha256=xuAsQPUC9aPIYehR3M07Caw-qMXU1ojuOobnce9vDCg,635392
numpy/random/mtrand.pyi,sha256=2e8aUstFMyrLOtQSV_SwgtM_F2UzmAF-DWKZH2xRocM,22676
numpy/random/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_direct.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_extending.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_generator_mt19937_regressions.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_random.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_randomstate.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_randomstate_regression.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_regression.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_seed_sequence.cpython-313.pyc,,
numpy/random/tests/__pycache__/test_smoke.cpython-313.pyc,,
numpy/random/tests/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/random/tests/data/__pycache__/__init__.cpython-313.pyc,,
numpy/random/tests/data/generator_pcg64_np121.pkl.gz,sha256=EfQ-X70KkHgBAFX2pIPcCUl4MNP1ZNROaXOU75vdiqM,203
numpy/random/tests/data/generator_pcg64_np126.pkl.gz,sha256=fN8deNVxX-HELA1eIZ32kdtYvc4hwKya6wv00GJeH0Y,208
numpy/random/tests/data/mt19937-testset-1.csv,sha256=bA5uuOXgLpkAwJjfV8oUePg3-eyaH4-gKe8AMcl2Xn0,16845
numpy/random/tests/data/mt19937-testset-2.csv,sha256=SnOL1nyRbblYlC254PBUSc37NguV5xN-0W_B32IxDGE,16826
numpy/random/tests/data/pcg64-testset-1.csv,sha256=wHoS7fIR3hMEdta7MtJ8EpIWX-Bw1yfSaVxiC15vxVs,24840
numpy/random/tests/data/pcg64-testset-2.csv,sha256=6vlnVuW_4i6LEsVn6b40HjcBWWjoX5lboSCBDpDrzFs,24846
numpy/random/tests/data/pcg64dxsm-testset-1.csv,sha256=Fhha5-jrCmRk__rsvx6CbDFZ7EPc8BOPDTh-myZLkhM,24834
numpy/random/tests/data/pcg64dxsm-testset-2.csv,sha256=mNYzkCh0NMt1VvTrN08BbkpAbfkFxztNcsofgeW_0ns,24840
numpy/random/tests/data/philox-testset-1.csv,sha256=QvpTynWHQjqTz3P2MPvtMLdg2VnM6TGTpXgp-_LeJ5g,24853
numpy/random/tests/data/philox-testset-2.csv,sha256=-BNO1OCYtDIjnN5Q-AsQezBCGmVJUIs3qAMyj8SNtsA,24839
numpy/random/tests/data/sfc64-testset-1.csv,sha256=sgkemW0lbKJ2wh1sBj6CfmXwFYTqfAk152P0r8emO38,24841
numpy/random/tests/data/sfc64-testset-2.csv,sha256=mkp21SG8eCqsfNyQZdmiV41-xKcsV8eutT7rVnVEG50,24834
numpy/random/tests/data/sfc64_np126.pkl.gz,sha256=MVa1ylFy7DUPgUBK-oIeKSdVl4UYEiN3AZ7G3sdzzaw,290
numpy/random/tests/test_direct.py,sha256=PI1C5R_WQGagdQ65sS74o_nq3ovYSDjExIDu9r3jY7k,20536
numpy/random/tests/test_extending.py,sha256=zZBAB6VvMh-JO6kc_Fco8C4bl-wTw_GY_BCoTg-kQ-M,4561
numpy/random/tests/test_generator_mt19937.py,sha256=ms_yBBSkxUKT0F7kjPM-PKwTi6SZvKMnmBdYkQS8a2E,120085
numpy/random/tests/test_generator_mt19937_regressions.py,sha256=5wlQqn6jdLwPbGNZrF3RPwLn_xRj2CCA6DY167dHN7c,8300
numpy/random/tests/test_random.py,sha256=TW-ikZicDVgTi9WeZOQwLCCCZ_Q_gWAom6PoztXSZ5k,71901
numpy/random/tests/test_randomstate.py,sha256=RrgFeK2r5JcD4K8paWObS8nKufdGumLN2fdnvp974kI,87399
numpy/random/tests/test_randomstate_regression.py,sha256=8FL4sxX1D1oMVX_F9u5vR8Zazo5V0Yj4bL7zsh57V-Y,8215
numpy/random/tests/test_regression.py,sha256=_eoEa-QIYh33tESahMHsVZtCy9W_s5T5RPzI6QYS7LY,5611
numpy/random/tests/test_seed_sequence.py,sha256=zWUvhWDxBmTN2WteSFQeJ29W0-2k3ZUze_3YtL4Kgms,3391
numpy/random/tests/test_smoke.py,sha256=StTxeemamKeE_H_UHQWyDxIXJSbLQI4Yr5sDp3y6ZH4,28992
numpy/rec/__init__.py,sha256=SMM69A-UzX5LD6JxSYXO-M9t4grwzRcqSAXXuMU5PSY,85
numpy/rec/__init__.pyi,sha256=lPzA1S5UmKd5MvDDBb-afONgZYl0Gr3l5LxPB7Qyc_I,368
numpy/rec/__pycache__/__init__.cpython-313.pyc,,
numpy/strings/__init__.py,sha256=NLFxhadn513TAXf8kgVguCvmyzXnP1JpVnNJtqfErX4,85
numpy/strings/__init__.pyi,sha256=1Lax4CbaTiyckJDEl0FluWFnv7GZyOh5hxMnEVuNBmo,1390
numpy/strings/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/__init__.py,sha256=ENc09IN_D74xNvH33Z65Q2dkaSEvljHF_tz-BV-g_dU,617
numpy/testing/__init__.pyi,sha256=hzSq3lVZ2gZbxMrQXNP3PaetjgJyKnfg50mkjTB8jXg,2147
numpy/testing/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/__pycache__/overrides.cpython-313.pyc,,
numpy/testing/__pycache__/print_coercion_tables.cpython-313.pyc,,
numpy/testing/_private/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__init__.pyi,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/_private/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/_private/__pycache__/extbuild.cpython-313.pyc,,
numpy/testing/_private/__pycache__/utils.cpython-313.pyc,,
numpy/testing/_private/extbuild.py,sha256=ce56g9xEaJHUo5CqcmcpnUksdcS6tW76BNoAfGnxysg,8358
numpy/testing/_private/extbuild.pyi,sha256=FWRL9bv2CK1FpFNLGXEJLvoZN6jgdQNnb62EENQ_u6Y,651
numpy/testing/_private/utils.py,sha256=HU-1SLzJMa-OnJttrcLlA4UcY-FBoF7uhcxLMkNCt1s,98460
numpy/testing/_private/utils.pyi,sha256=930ijrCmd_ZISmL4rGWSSutytCDzAiT-JJPl4fka2yY,13463
numpy/testing/overrides.py,sha256=FRkp9cLvEwCdXWLinUH3hGf_u9SIzZk17QcRQfITZyk,2216
numpy/testing/overrides.pyi,sha256=LMYa6hii8jPmR_eC-LHNrz3irrImvZcW29NxCkfgzNk,408
numpy/testing/print_coercion_tables.py,sha256=BGTgZxvxnUNYqOwsceMR9xQ1LD6QUePsKLBsq8c8Vyo,6424
numpy/testing/print_coercion_tables.pyi,sha256=O4nFjoyQ4AvDO2BrzsFi4QKaxsgmf1KDKAS-IEemPxw,848
numpy/testing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/testing/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/testing/tests/__pycache__/test_utils.cpython-313.pyc,,
numpy/testing/tests/test_utils.py,sha256=xoQskILg4xhRkfHLsljkXfDHYjTtT1QkLyvNaV2KBVk,72385
numpy/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/tests/__pycache__/test__all__.cpython-313.pyc,,
numpy/tests/__pycache__/test_configtool.cpython-313.pyc,,
numpy/tests/__pycache__/test_ctypeslib.cpython-313.pyc,,
numpy/tests/__pycache__/test_lazyloading.cpython-313.pyc,,
numpy/tests/__pycache__/test_matlib.cpython-313.pyc,,
numpy/tests/__pycache__/test_numpy_config.cpython-313.pyc,,
numpy/tests/__pycache__/test_numpy_version.cpython-313.pyc,,
numpy/tests/__pycache__/test_public_api.cpython-313.pyc,,
numpy/tests/__pycache__/test_reloading.cpython-313.pyc,,
numpy/tests/__pycache__/test_scripts.cpython-313.pyc,,
numpy/tests/__pycache__/test_warnings.cpython-313.pyc,,
numpy/tests/test__all__.py,sha256=JziA96KUyXwWCPExbQcJBqe_RU1xQVrVwi1xhO8tzqM,230
numpy/tests/test_configtool.py,sha256=goqOIpRq8Hrig_d6vxZGu8zluQManELhkGGDl3g9qto,1598
numpy/tests/test_ctypeslib.py,sha256=PSiQsEpT3CoLFp56zntAEkaJJ1VMHkvE0pr8-infzKM,12728
numpy/tests/test_lazyloading.py,sha256=vsobnlXKUfdMdqMIAZBF_DRSbYNhYF3Za4cYv-J7qHA,1196
numpy/tests/test_matlib.py,sha256=TUaQmGoz9fvQQ8FrooTq-g9BFiViGWjoTIGQSUUF6-Y,1910
numpy/tests/test_numpy_config.py,sha256=F0vWlR3yQyfWI3XfCxKYc6f6k3ldLDypCHbUGU_gy8E,1277
numpy/tests/test_numpy_version.py,sha256=n4cggUNnM9okmtxwyhYBWBFwJvKpY7NzYxMgrNwRU40,1808
numpy/tests/test_public_api.py,sha256=bn39YfPIbaVvn4cOsw7escA3F-iWLAaMKBhgeSvAXYE,28474
numpy/tests/test_reloading.py,sha256=spEldUm_nmV0tBoUG53a2ORCOjwfltimpKfGGTqa7pI,2441
numpy/tests/test_scripts.py,sha256=6rZN5bnGpeR4vEjLBiKEUMXJiE2NVnbY1Q8xKPlOqA8,1692
numpy/tests/test_warnings.py,sha256=iAipwlsADKIY0BdRHd6oRv4RzOI0p0nxcqSr9DoqeLI,2422
numpy/typing/__init__.py,sha256=rGl883L4FnRPSzNe1Zyz7_KrHvxIMobSMoLuGPPhKNI,5442
numpy/typing/__pycache__/__init__.cpython-313.pyc,,
numpy/typing/__pycache__/mypy_plugin.cpython-313.pyc,,
numpy/typing/mypy_plugin.py,sha256=BJQGuyCEXpt-DSVgwiG1LQWDoXhbWTBRqDA3q8kk2wI,6669
numpy/typing/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
numpy/typing/tests/__pycache__/__init__.cpython-313.pyc,,
numpy/typing/tests/__pycache__/test_isfile.cpython-313.pyc,,
numpy/typing/tests/__pycache__/test_runtime.cpython-313.pyc,,
numpy/typing/tests/__pycache__/test_typing.cpython-313.pyc,,
numpy/typing/tests/data/fail/arithmetic.pyi,sha256=C-C5EOxTviVtzFk77RdVHTrEyQEJvUp2wrzlR9nQMOU,3904
numpy/typing/tests/data/fail/array_constructors.pyi,sha256=mrcArR9EVNE4-9yKg-SgVv_Yp-4DpZ1Q_0cHiRwXRtI,1163
numpy/typing/tests/data/fail/array_like.pyi,sha256=MUIx6Oc5bJeebr-TC4FhZFXnX9pJ5gQDv8moHmPek10,471
numpy/typing/tests/data/fail/array_pad.pyi,sha256=JGCMd_sRBYlsPQ2d7EfLaNooTsg1P0jBuD5Ds2MeXAg,138
numpy/typing/tests/data/fail/arrayprint.pyi,sha256=KAbzVtw1V65ImeO4MhlejQt8yYB3mhCHwt0eqVqqoTY,602
numpy/typing/tests/data/fail/arrayterator.pyi,sha256=tRPWjCh1-sg5FXAyYeTbHSR983JUFlecRNcustDLt4E,484
numpy/typing/tests/data/fail/bitwise_ops.pyi,sha256=QglRyKkdf96Z-klBfGQ1JSmtOFk3yeSDFz0MqKS-rj0,604
numpy/typing/tests/data/fail/char.pyi,sha256=m8SxJUaMSj2SWFHhjtJHj0b1KMPg7f1tXBjpPG_pEso,2781
numpy/typing/tests/data/fail/chararray.pyi,sha256=inRqI3ZlDZ-R6Wpe4VoQnNzuO874E6SNcbzM9bz4xjw,2368
numpy/typing/tests/data/fail/comparisons.pyi,sha256=xrNXGulq1kVRufLUB7nG95g_YNr_wR5hbIdhy0tkRMc,849
numpy/typing/tests/data/fail/constants.pyi,sha256=3IZ6T9p4n61qIXngrHB8VqEaqloxcNmbUz3YcSqNSXI,88
numpy/typing/tests/data/fail/datasource.pyi,sha256=mX9ucsgNXNekVFuRVzBjleA-p8GpuwpbsHqiG6a9CpA,420
numpy/typing/tests/data/fail/dtype.pyi,sha256=ltT4BFaX_KTVdRLw2dMg3_OiSNYjDSNrXsxby6eeLTw,354
numpy/typing/tests/data/fail/einsumfunc.pyi,sha256=dYOaJ0J4EUzdyUBikKHie99K8SMaYrlqN3R9aDcMeJ4,499
numpy/typing/tests/data/fail/flatiter.pyi,sha256=u4-JnRsydg5BW3OcA9we8MXLJ6F5cuaxxw0BrHVA9kY,891
numpy/typing/tests/data/fail/fromnumeric.pyi,sha256=cN_nAgj2y2_wkErPsP1zAxG0CmHQmmeO4g7qkA9FsWY,5868
numpy/typing/tests/data/fail/histograms.pyi,sha256=JteTXgK_kXD8UPdihMZ_T2VcM3rTBj6t-MMRP8UHvhw,379
numpy/typing/tests/data/fail/index_tricks.pyi,sha256=63ADYRCVtf0Dapc2dJpYJZDSIXK3MhhW_1lG30d3-RY,523
numpy/typing/tests/data/fail/lib_function_base.pyi,sha256=uvVKoZP0Mx-8V8DMCnLWoe8lk6eRT3eSAxqNFpylwEQ,2751
numpy/typing/tests/data/fail/lib_polynomial.pyi,sha256=PM1TD9h4tFNeMp4y6HlXHKuAHDW0bfNHw0UWLUHnLVk,928
numpy/typing/tests/data/fail/lib_utils.pyi,sha256=chR5zMEM5KI2Aw0LPIlIC8CnEcPIHwyKMLzbPhXNYXU,99
numpy/typing/tests/data/fail/lib_version.pyi,sha256=JWtuTLcjkZpGfXshlFpJO5vINxawn9S-mxLGH0-7kcw,164
numpy/typing/tests/data/fail/linalg.pyi,sha256=j6GGpOENz0nuZsza0Dyfy6MtjfRltqrbY8K_7g5H92I,1370
numpy/typing/tests/data/fail/memmap.pyi,sha256=eAX-nEKtOb06mL8EPECukmL8MwrehSVRu5TBlHiSBaQ,164
numpy/typing/tests/data/fail/modules.pyi,sha256=HYfnYNKIRwGg2caw19iqN1MDcctFMQKlE4mqoasWDaM,638
numpy/typing/tests/data/fail/multiarray.pyi,sha256=AMsYk58-B30xQTHirBGAC6vykmauw-S7H_YiHSLOAQA,1696
numpy/typing/tests/data/fail/ndarray.pyi,sha256=5A83TCpAmaUC0rtOU0NVG0vsNfKo_-1SF5qtVT7eqoc,415
numpy/typing/tests/data/fail/ndarray_misc.pyi,sha256=ew0rklpnwM-57zZTCY7nczMS_tj8y7rxKTcnmjayPlU,1036
numpy/typing/tests/data/fail/nditer.pyi,sha256=We6p5_nmfUdd_4CtwYZc5O7MTSMyM-Xw7mEUzdKPcP4,333
numpy/typing/tests/data/fail/nested_sequence.pyi,sha256=7E1zJ2SZIF0ldbEmjtA_Bp6cV4Q-cS4Op0BJN3Vi3rc,444
numpy/typing/tests/data/fail/npyio.pyi,sha256=CT-NXoisYmIy-WBGaZkCm8zHPCL2Ju5Moy021vnEhIU,653
numpy/typing/tests/data/fail/numerictypes.pyi,sha256=wPJaHwMdiX1tJLdnYAgZ5z42tEhX-8EtGfWKU81czf4,125
numpy/typing/tests/data/fail/random.pyi,sha256=v_Y-EfhC7PC8E3AH-v-AfiZVlJDSShL77WQ3yXWx5iE,2883
numpy/typing/tests/data/fail/rec.pyi,sha256=BxH41lR1wLvLrlash9mzkPFngDAXSPQQXvuHxYylHAI,721
numpy/typing/tests/data/fail/scalars.pyi,sha256=gN2pS35JX6MOCZTzL_1ml53510Kjr2dfVclLZrOwCpE,2951
numpy/typing/tests/data/fail/shape.pyi,sha256=-SzfxgevV7APDLlq-Sh8KzsKdCjHUb5GXEeJ9H6tacQ,143
numpy/typing/tests/data/fail/shape_base.pyi,sha256=ZU1KSP0k-i-npwIMUhp42-EMzrdZhOqPEnV8ah-ZJ6U,160
numpy/typing/tests/data/fail/stride_tricks.pyi,sha256=L0fJGun6CDq24yNdw2zeNVGGcIpEOyP2dmWj1pEbMz8,324
numpy/typing/tests/data/fail/strings.pyi,sha256=XAiAwOERfMOL9INbER33qH-7_5rPGX4eubGcWsl36Fc,2429
numpy/typing/tests/data/fail/testing.pyi,sha256=GYfvI1A2pB1Ii2jFVL-WGqRVimbFS2oCijmoWVbMAgw,1371
numpy/typing/tests/data/fail/twodim_base.pyi,sha256=wzd-h1ye2BhMdIHlQ0ZcHfgYRBHVX2GJ3WGfMk5euPg,935
numpy/typing/tests/data/fail/type_check.pyi,sha256=0KG0c2LNUbUFChTYtbJ38eJUmfvUJl4Cn5G0vh1Bkrw,392
numpy/typing/tests/data/fail/ufunc_config.pyi,sha256=WzZzWJ-cC39qAzak3Cf--XIZX11MqwsEa3bYYyzqsvY,755
numpy/typing/tests/data/fail/ufunclike.pyi,sha256=89Fjsr7vmurRl90mVbC5L0xOwRIk0jg4mJrgkTDn4eM,648
numpy/typing/tests/data/fail/ufuncs.pyi,sha256=2ATU0I4ZF8DB3vyodRDJIuXnXb-CcQpt-l4Kn00kJxA,493
numpy/typing/tests/data/fail/warnings_and_errors.pyi,sha256=4sTfiur0rV5CpjlYJC_1WV3KPnovteiImffvpYh19eU,190
numpy/typing/tests/data/misc/extended_precision.pyi,sha256=RTsXUAM9iKX_L-iviwFVuUwKcqX9N8sRW5ZHAXjYtjc,909
numpy/typing/tests/data/mypy.ini,sha256=TIOl-4bxGj7Q5DAYamOE_pBLnXMQf1quG7Maena9CRY,295
numpy/typing/tests/data/pass/__pycache__/arithmetic.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_constructors.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/array_like.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayprint.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/arrayterator.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/bitwise_ops.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/comparisons.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/dtype.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/einsumfunc.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/flatiter.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/fromnumeric.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/index_tricks.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_user_array.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_utils.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/lib_version.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/literal.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ma.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/mod.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/modules.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/multiarray.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_conversion.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_misc.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ndarray_shape_manipulation.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/nditer.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/numeric.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/numerictypes.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/random.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/recfunctions.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/scalars.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/shape.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/simple_py3.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunc_config.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufunclike.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/ufuncs.cpython-313.pyc,,
numpy/typing/tests/data/pass/__pycache__/warnings_and_errors.cpython-313.pyc,,
numpy/typing/tests/data/pass/arithmetic.py,sha256=T2IizTDJ0bkGhPk5rsD5dpeEmNfPxWrVpmOB1eyY7As,8043
numpy/typing/tests/data/pass/array_constructors.py,sha256=MGzgCt7uTeC_b7wU2aPlvTuDzXfgOujx_lR0Vqfpny8,2584
numpy/typing/tests/data/pass/array_like.py,sha256=qLqVJLU8bjSIB3xFNCzRNAcozCWRAVLagiYYG7ewQJA,1101
numpy/typing/tests/data/pass/arrayprint.py,sha256=NTw1gJ9v3TDVwRov4zsg_27rI-ndKuG4mDidBWEKVyc,803
numpy/typing/tests/data/pass/arrayterator.py,sha256=z4o0H08T7tbzzMWhu5ZXdVqbivjBicuFgRHBk_lpOck,420
numpy/typing/tests/data/pass/bitwise_ops.py,sha256=8lfjgayfTDDcWi1O-rnxLu4FZqvskvGHvFXJpMQWQgc,1095
numpy/typing/tests/data/pass/comparisons.py,sha256=-NSAhFNN3kWqu2CZqt2pq3kflTx6nDCWxkO3JIYl5NI,3613
numpy/typing/tests/data/pass/dtype.py,sha256=YRsTwKEQ5iJtdKCEQIybU_nL8z8Wq9hU-BZmEO7HjQE,1127
numpy/typing/tests/data/pass/einsumfunc.py,sha256=CXdLvQsU2iDqQc7d2TRRCSwguQzJ0SJDFn23SDeOOuY,1406
numpy/typing/tests/data/pass/flatiter.py,sha256=2xtMPvDgfhgjZIqiN3B3Wvy6Q9oBeo9uh4UkCAQNmwg,190
numpy/typing/tests/data/pass/fromnumeric.py,sha256=bP0hEQYYQJOn7-ce0rAf8cvuxZX3Ja6GSSlCtNhEBUM,4263
numpy/typing/tests/data/pass/index_tricks.py,sha256=RyuEtqyZVlK9j403DVjMZFd80mvt-VAMi1uGvXurc0c,1462
numpy/typing/tests/data/pass/lib_user_array.py,sha256=K69fg9dI5BaglzpiJh13swGHuyx3LBW_zmzBBOB1aWw,612
numpy/typing/tests/data/pass/lib_utils.py,sha256=XEc0v7bwES-C5D4GkSJQSSTSAl5ng7tq6tCWj3jxbCM,336
numpy/typing/tests/data/pass/lib_version.py,sha256=TlLZK8sekCMm__WWo22FZfZc40zpczENf6y_TNjBpCw,317
numpy/typing/tests/data/pass/literal.py,sha256=sWAaQyBnm3jIEZrdqWe58U2sCzeE7mUSlG8tWIcQzRc,1555
numpy/typing/tests/data/pass/ma.py,sha256=LfK4LXCWLLK5q0c1Me8STWbhGj9b_46LYvZwXGpaEjQ,179
numpy/typing/tests/data/pass/mod.py,sha256=L1qLwjdrRo9Tx7mxWpf_ugdKdUprDYhPRbCvQd5QjXY,1725
numpy/typing/tests/data/pass/modules.py,sha256=buzLurat4TIGmJuW3mGsGk7dKNmpBDfQOWWQXFfb9Uc,670
numpy/typing/tests/data/pass/multiarray.py,sha256=i6VU-VN96Q16mRGzVoY3oTE2W1z16GOGTOVFxWGRacM,1407
numpy/typing/tests/data/pass/ndarray_conversion.py,sha256=6TnvucV8Vtte7dGWihx7YmrHlNOanqmLJIH1W8Wok0E,1612
numpy/typing/tests/data/pass/ndarray_misc.py,sha256=nI6loZ67OjL3Uzu0AQYsHrI-a_gq5SCzVzJqSiTKDc0,3662
numpy/typing/tests/data/pass/ndarray_shape_manipulation.py,sha256=yaBK3hW5fe2VpvARkn_NMeF-JX-OajI8JiRWOA_Uk7Y,687
numpy/typing/tests/data/pass/nditer.py,sha256=1wpRitCNZKCC3WJVrFSh22Z1D8jP2VxQAMtzH8NcpV8,67
numpy/typing/tests/data/pass/numeric.py,sha256=E6JrIBZ8yaEDn4hkaePxcdYdkC6VKZUKSu_Z65Rsqkg,1720
numpy/typing/tests/data/pass/numerictypes.py,sha256=JaCjk4zQPOI67XzqGyi3dI-GUMFM2AvDuniwzSQ7_Rk,348
numpy/typing/tests/data/pass/random.py,sha256=wYwClLry-mN-QvaYg6AFGhwDuvoKQv-bl94fq10sL3k,63321
numpy/typing/tests/data/pass/recfunctions.py,sha256=aeOxXwMkhc0aXyhmg4dW2QvekHDGAaYYTHVaQwrfKGY,5199
numpy/typing/tests/data/pass/scalars.py,sha256=KfCYjDIxR9G2ypqCQJKQOuBWxiLqrGCV38q0JN3TqvA,3973
numpy/typing/tests/data/pass/shape.py,sha256=Wr_y3KiVe5elHXLChRVupFvE_haiEFilCvk-ESR1Rcg,470
numpy/typing/tests/data/pass/simple.py,sha256=aXvt9iCOV1lhQR11xVWgQIXXyXRHKOBfCtTjthZFtM0,2919
numpy/typing/tests/data/pass/simple_py3.py,sha256=OBpoDmf5u4bRblugokiOZzufESsEmoU03MqipERrjLg,102
numpy/typing/tests/data/pass/ufunc_config.py,sha256=gmMTPrq8gLXJZSBQoOpJcgzIzWgMx-k_etKPV4KSTJk,1269
numpy/typing/tests/data/pass/ufunclike.py,sha256=jxTR61d0bmFg7JHZmw992ccRua00u4XWJYtcQRJwFS0,1172
numpy/typing/tests/data/pass/ufuncs.py,sha256=gvdcCNoGUfN0CnQmn6k1j6ghdt8zGkJdcRcgctmU48A,438
numpy/typing/tests/data/pass/warnings_and_errors.py,sha256=q3c1SmMwhyYLYQsLjK02AXphk3-96YltSTdTfrElJzQ,167
numpy/typing/tests/data/reveal/arithmetic.pyi,sha256=OU8qyQpYk_4z8YO4Zl54qikYYpIHjRPq9doH8BYlWaY,24884
numpy/typing/tests/data/reveal/array_api_info.pyi,sha256=zeNMHOn1HoTFaJTXkz5_GuFg3OvRa7W-gdxdJl1FPG4,3119
numpy/typing/tests/data/reveal/array_constructors.pyi,sha256=jq0TvzyKKqLq3DWcuvhsI3oIbBtj6ikhO82S_ZWjI2I,12817
numpy/typing/tests/data/reveal/arraypad.pyi,sha256=viQwv8d_Hsc5nhIqC4cGkRWbmXaqf3ehutPnmOleDkY,712
numpy/typing/tests/data/reveal/arrayprint.pyi,sha256=JZmfW3bqJWY6TUM3JDMyVBS3cSTopPkBF5O8yzD3kiU,844
numpy/typing/tests/data/reveal/arraysetops.pyi,sha256=gA2uqkyPESPjasbca_mZ-e41XkecogZVOpRDSZpSl38,4496
numpy/typing/tests/data/reveal/arrayterator.pyi,sha256=a0gVqOyltcfjoBqwXiouAt3ghZUwFUZo0s4xg1VZGrI,1098
numpy/typing/tests/data/reveal/bitwise_ops.pyi,sha256=8snEpx2ci_08vZuWWHZ81KrRflUxSGvtae2_9d5uKWo,5219
numpy/typing/tests/data/reveal/char.pyi,sha256=AyDGKchLOkZ8TFaTl7DTb1-zLCNX1bsjHlMWlJW0QRU,11065
numpy/typing/tests/data/reveal/chararray.pyi,sha256=0ZJp_G4AzrO0r2ntt082iCbeZ2cnoB1iPB4YoFvYNuc,6787
numpy/typing/tests/data/reveal/comparisons.pyi,sha256=kMeqY8sAwL8_joBBTMo65V1wCAfQCjppBkmhN_ax0Ds,7491
numpy/typing/tests/data/reveal/constants.pyi,sha256=pg8eBcAYp-7Xc-5iAgzPR8c4qpo89f9Cj2LKBatsu7Q,377
numpy/typing/tests/data/reveal/ctypeslib.pyi,sha256=42zUf_JaEvC2F8JJtCv2G2YLR51L0V_wOpxulQKBJYk,4830
numpy/typing/tests/data/reveal/datasource.pyi,sha256=H2QtFrQWad_gRlGkTZ7subBfTHjoAhwzUCdYuS_d9C0,638
numpy/typing/tests/data/reveal/dtype.pyi,sha256=f-Ev24OaSP6ChpI6Xn5j4VOUKGoX6Ixvs95yJFjHuf0,5353
numpy/typing/tests/data/reveal/einsumfunc.pyi,sha256=wx3i7hQdY1mhmu6fnvGLYlN0yVByZT-fPLEMhfEkLls,1997
numpy/typing/tests/data/reveal/emath.pyi,sha256=TC5sIisHUbcS0EDnVfokZu072bfo2qy1lF6XYQBoTeI,2391
numpy/typing/tests/data/reveal/fft.pyi,sha256=02MXu-BxNyrIcSVBXLgC0poNsNGiXDWhWkGbY2zvZmw,1700
numpy/typing/tests/data/reveal/flatiter.pyi,sha256=0L4ImsC4qP-bujm0czaLAMs_J57bUEUJL6CNi8L44Gw,1426
numpy/typing/tests/data/reveal/fromnumeric.pyi,sha256=cj1x-A12dZaf7wRzFkiNzsrRpOGlSXzcLftMYeqngBg,15260
numpy/typing/tests/data/reveal/getlimits.pyi,sha256=jp4uMJIJ2QooTSo765kdY9x1sphgankB4soyWL-CJHs,1635
numpy/typing/tests/data/reveal/histograms.pyi,sha256=GMA2nwIztaW6nYbJ8r6wNLiQvL0V0CssJ4uB_qEfiuw,1314
numpy/typing/tests/data/reveal/index_tricks.pyi,sha256=mLlymGWD-8uafZ4iDEgeeIi2BwAts5_w-6koRPdI2fQ,3343
numpy/typing/tests/data/reveal/lib_function_base.pyi,sha256=pEVGWIuBbo8ymrIybovkhyNFL8iYF7LC-01O932gltQ,10087
numpy/typing/tests/data/reveal/lib_polynomial.pyi,sha256=5ipkWMQOnEq59oxAdHHqD-9Ion5CnfzcVc69K5y-KPs,6041
numpy/typing/tests/data/reveal/lib_utils.pyi,sha256=hc8SEa5GX2bvey1lPdjkHeQJsGzFTbYaqvkXQWzFGZA,466
numpy/typing/tests/data/reveal/lib_version.pyi,sha256=nAmE8-EYApx_o3Ih6XNcnGAT9gmwYn3emcH_jMjvqF0,603
numpy/typing/tests/data/reveal/linalg.pyi,sha256=v32iuvfKkf9b68M4EzApRNb33FYYK4l2h8WtA_DYSoM,6366
numpy/typing/tests/data/reveal/matrix.pyi,sha256=iIyDGTXx-tNFvc3_cpTsh-5qdCT1RSZG7hoHku79BlA,3122
numpy/typing/tests/data/reveal/memmap.pyi,sha256=NzJkfVKfej4RQeVY9K-hkN_h4_5AOy6k6WtXT8FMW1U,775
numpy/typing/tests/data/reveal/mod.pyi,sha256=gdJr_fx0lxU9ISMQMtwb1_6J0J_N5b-04mPVUPSUpxk,7792
numpy/typing/tests/data/reveal/modules.pyi,sha256=bVHJ0-4XHxHwF4G6YFtQx4E7i9NjXJX4Xl1-2suWxL4,1922
numpy/typing/tests/data/reveal/multiarray.pyi,sha256=EWlKpzai4sIdRSi9HX-jfOfBWQY79e2L3gdcb3TSV5A,8061
numpy/typing/tests/data/reveal/nbit_base_example.pyi,sha256=L623ocsRjDAvFWEBnV1-D6E68v0beBDvXQ5tTDGSjaU,610
numpy/typing/tests/data/reveal/ndarray_assignability.pyi,sha256=bWJadq5zn_6ya0mSryzYh2jmCqFS6y_5oVukAvUj-1w,2777
numpy/typing/tests/data/reveal/ndarray_conversion.pyi,sha256=dP7RIN2JoQLyiATFYxxhH81ig76SXVCJXAxGhk7LPkE,3465
numpy/typing/tests/data/reveal/ndarray_misc.pyi,sha256=mUys3JQqq9AjiaJ9VaEE6mJ1806iqy9Gy1um7DHGqtk,8137
numpy/typing/tests/data/reveal/ndarray_shape_manipulation.pyi,sha256=GOMRYAh2un_a9YMnhNus1icBEctWBM-JiE0XUGenADk,1444
numpy/typing/tests/data/reveal/nditer.pyi,sha256=6sV8LI8D5F9nNWwbcLBQSWubRbnIZ-5EVveHi3G_bJs,1984
numpy/typing/tests/data/reveal/nested_sequence.pyi,sha256=IHoDdBHrPR9t7Hlu5uS7Gwmdhpry1vnJxjoZYACWbjk,674
numpy/typing/tests/data/reveal/npyio.pyi,sha256=fW3S6E7_Lc290CD_Cu_nwLiA7rDzyZTWsNp7opI5Ono,3608
numpy/typing/tests/data/reveal/numeric.pyi,sha256=1viOAuH2hv0NietbvxyVEpeQJacMqy7ck-mQpRHxEB4,6218
numpy/typing/tests/data/reveal/numerictypes.pyi,sha256=UfImexHrS0BEJIHdtdHnDCFaeokhEKtA08_ArfhsXus,1414
numpy/typing/tests/data/reveal/polynomial_polybase.pyi,sha256=lNwOtFZ9wv3CIuPJwnwUNHyAStBv97LP423erNq90X8,8220
numpy/typing/tests/data/reveal/polynomial_polyutils.pyi,sha256=BJX4_LBPqbPQ1eY-br4SqHUHO9zvIrOxa_J2TKrufWg,10984
numpy/typing/tests/data/reveal/polynomial_series.pyi,sha256=NMH4MqVHDNNI1mLewAZg9Zin5EoDoYik_Q-YhbQa5gk,7268
numpy/typing/tests/data/reveal/random.pyi,sha256=8llrjYb6sRt7qblJiOniDKCrC1ERE3JNfenqIPX-2UI,105880
numpy/typing/tests/data/reveal/rec.pyi,sha256=sv6b2EJApQUcucb205S_GBtPVd675T_8VgdeVn78jqY,3944
numpy/typing/tests/data/reveal/scalars.pyi,sha256=Ch1zywWqKdpyvTJDRGRdfzZ6to-VxWI512MuNwnY00M,6642
numpy/typing/tests/data/reveal/shape.pyi,sha256=M0joDPodElAHLjI9FmofIgS45uSidjXOnfpbKwyBaZ8,307
numpy/typing/tests/data/reveal/shape_base.pyi,sha256=tn2gaV-VfJLPp18NH3qGxXVoOZXm4nZj4pKrF5IonlE,2100
numpy/typing/tests/data/reveal/stride_tricks.pyi,sha256=YFA73zIg8YRoDnDi6VKc-3CRZoOJZ_YhNlqiyqrthLE,1374
numpy/typing/tests/data/reveal/strings.pyi,sha256=TSRS1RV7KdjVMjfAwJr_ja60et1LqWWrdD-hop_mikQ,9608
numpy/typing/tests/data/reveal/testing.pyi,sha256=Ym8uuMNq1l6VqZsN43ghEL8DpZKFX4kisH-BV79xFNU,8683
numpy/typing/tests/data/reveal/twodim_base.pyi,sha256=g-fZ2g9vh4XYIltv2sSgsAtl0yg_EwW3NdhKIFFVG6o,4451
numpy/typing/tests/data/reveal/type_check.pyi,sha256=i-8YsIOGxgxdleMzwO8ctaLfYzcnBUKDBY7TiwK-IHA,2790
numpy/typing/tests/data/reveal/ufunc_config.pyi,sha256=w4XdMT8Rz2pu6XLmVhb-F4hnCb--A_dYYjBJCtWVZ5s,1222
numpy/typing/tests/data/reveal/ufunclike.pyi,sha256=7-LodGrHthBQuR42rQlXw3nQsVqHGYdxzu73B2ps1tY,1266
numpy/typing/tests/data/reveal/ufuncs.pyi,sha256=mSu8DtwP2b-l45nrxWwKiNGeALQbG6qAKBYg_OLOqWc,4944
numpy/typing/tests/data/reveal/warnings_and_errors.pyi,sha256=TqQe7189p4B7PAbuIQbGNkLykuCpq2ngCB2Bscsh_bw,471
numpy/typing/tests/test_isfile.py,sha256=slpVB1kHtrG5unlgYxl94Q_kOzDBPnDtFZQhLZdq9JM,897
numpy/typing/tests/test_runtime.py,sha256=p-Ydvt0Rt6mPHmAKYOOAGxxXQnjoARJSVZmViKMAX0A,3384
numpy/typing/tests/test_typing.py,sha256=ZVc9wJgtAKRX6S1lkSiR6Y9w_Dxwl0TLN-rAvzJBSFw,8594
numpy/version.py,sha256=ao42Ds0h0708A3GuP1EUqJtvGwRBrupyVvp2QNYBXbQ,304
numpy/version.pyi,sha256=WPYF3zFF92LnJu7CGTRsh4osMyXBuQRpMvAuoxKMrbw,408
