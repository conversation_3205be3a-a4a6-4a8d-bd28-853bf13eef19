#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证脚本 - 测试完整的自动化流程是否能正常工作
"""

import sys
import os
import time

def test_complete_workflow():
    """测试完整的自动化工作流程"""
    print("🔍 测试完整自动化工作流程...")
    
    try:
        # 导入主控制器
        import main_controller
        print("✅ 主控制器导入成功")
        
        # 创建控制器实例
        controller = main_controller.WeChatMainController()
        print("✅ 控制器实例创建成功")
        
        # 检查6个执行步骤是否都存在
        steps = [
            "WINDOW_MANAGEMENT",
            "MAIN_INTERFACE", 
            "SIMPLE_ADD",
            "IMAGE_RECOGNITION",
            "FRIEND_REQUEST",
            "FREQUENCY_HANDLING"
        ]
        
        print("\n🔍 检查6个执行步骤:")
        for step in steps:
            if hasattr(main_controller.ExecutionStep, step):
                print(f"✅ {step} - 步骤存在")
            else:
                print(f"❌ {step} - 步骤不存在")
                return False
        
        # 检查关键方法是否存在
        key_methods = [
            "execute_step",
            "run_automation",
            "process_friend_list"
        ]
        
        print("\n🔍 检查关键方法:")
        for method in key_methods:
            if hasattr(controller, method):
                print(f"✅ {method} - 方法存在")
            else:
                print(f"❌ {method} - 方法不存在")
                return False
        
        print("\n🎉 完整工作流程验证成功！")
        print("💡 所有6个自动化步骤都已正确包含")
        print("💡 主控制器的所有关键方法都存在")
        print("💡 程序应该能够完整执行自动化流程而不会在第2步中断")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流程测试失败: {e}")
        return False

def test_modules_availability():
    """测试所有modules模块的可用性"""
    print("\n🔍 测试modules模块可用性...")
    
    modules_list = [
        ("config_utils", "配置工具"),
        ("data_manager", "数据管理"),
        ("frequency_error_handler", "频率错误处理"),
        ("friend_request_window", "好友请求窗口"),
        ("log_deduplication_manager", "日志去重管理"),
        ("main_interface", "主界面"),
        ("mouse_visual_feedback", "鼠标视觉反馈"),
        ("wechat_auto_add_friend", "微信自动添加好友"),
        ("wechat_auto_add_simple", "微信简单添加"),
        ("window_manager", "窗口管理")
    ]
    
    success_count = 0
    for module_name, description in modules_list:
        try:
            module = __import__(f"modules.{module_name}", fromlist=[module_name])
            print(f"✅ {module_name} ({description}) - 可用")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name} ({description}) - 不可用: {e}")
    
    print(f"\n📊 模块可用性: {success_count}/{len(modules_list)}")
    return success_count == len(modules_list)

def test_resource_files():
    """测试资源文件是否正确包含"""
    print("\n🔍 测试资源文件...")
    
    import path_utils
    
    resource_files = [
        ("config.json", "配置文件"),
        ("window_safety_config.json", "窗口安全配置"),
        ("添加好友名单.xlsx", "好友名单Excel")
    ]
    
    success_count = 0
    for filename, description in resource_files:
        try:
            file_path = path_utils.get_resource_path(filename)
            if os.path.exists(file_path):
                print(f"✅ {filename} ({description}) - 存在")
                success_count += 1
            else:
                print(f"❌ {filename} ({description}) - 不存在")
        except Exception as e:
            print(f"❌ {filename} ({description}) - 错误: {e}")
    
    print(f"\n📊 资源文件: {success_count}/{len(resource_files)}")
    return success_count == len(resource_files)

def main():
    print("=" * 70)
    print("🧪 微信自动化精简版 - 最终验证测试")
    print("=" * 70)
    print("目标: 验证modules目录问题已解决，流程不会在第2步中断")
    print("=" * 70)
    
    # 显示环境信息
    print(f"🐍 Python版本: {sys.version.split()[0]}")
    print(f"📁 当前目录: {os.getcwd()}")
    
    if hasattr(sys, '_MEIPASS'):
        print(f"📦 打包环境: {sys._MEIPASS}")
    else:
        print("🔧 开发环境")
    
    # 执行测试
    tests = [
        ("modules模块可用性", test_modules_availability),
        ("资源文件完整性", test_resource_files),
        ("完整工作流程", test_complete_workflow),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print(f"{'='*50}")
        
        result = test_func()
        results.append((test_name, result))
    
    # 显示最终结果
    print("\n" + "=" * 70)
    print("📋 最终验证结果")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("\n🎉 所有验证测试通过！")
        print("✅ modules目录问题已完全解决")
        print("✅ 程序应该能够完整执行所有6个自动化步骤")
        print("✅ 流程不会再在第2步中断")
        print("✅ 所有依赖模块都已正确包含")
        print("\n💡 建议:")
        print("1. 运行 '微信自动化精简版.exe' 测试GUI界面")
        print("2. 使用实际微信窗口测试完整自动化流程")
        print("3. 验证所有6个步骤都能正常执行")
        return 0
    else:
        print("\n⚠️ 部分验证失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
