#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试modules目录是否正确包含和导入
"""

import sys
import os
from pathlib import Path

def test_modules_import():
    """测试所有modules模块是否能正确导入"""
    print("🔍 测试modules目录模块导入...")
    
    modules_to_test = [
        "modules.config_utils",
        "modules.data_manager", 
        "modules.frequency_error_handler",
        "modules.friend_request_window",
        "modules.log_deduplication_manager",
        "modules.main_interface",
        "modules.mouse_visual_feedback",
        "modules.wechat_auto_add_friend",
        "modules.wechat_auto_add_simple",
        "modules.window_manager"
    ]
    
    success_count = 0
    failed_modules = []
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name} - 导入成功")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name} - 导入失败: {e}")
            failed_modules.append(module_name)
        except Exception as e:
            print(f"⚠️ {module_name} - 其他错误: {e}")
            failed_modules.append(module_name)
    
    print(f"\n📊 导入测试结果:")
    print(f"成功: {success_count}/{len(modules_to_test)}")
    print(f"失败: {len(failed_modules)}")
    
    if failed_modules:
        print(f"失败模块: {', '.join(failed_modules)}")
        return False
    else:
        print("🎉 所有modules模块导入成功！")
        return True

def test_main_controller():
    """测试主控制器是否能正确导入"""
    print("\n🔍 测试主控制器...")
    
    try:
        import main_controller
        print("✅ main_controller - 导入成功")
        
        # 检查是否有WeChatMainController类
        if hasattr(main_controller, 'WeChatMainController'):
            print("✅ WeChatMainController类 - 存在")
            return True
        else:
            print("❌ WeChatMainController类 - 不存在")
            return False
            
    except ImportError as e:
        print(f"❌ main_controller - 导入失败: {e}")
        return False

def test_path_utils():
    """测试路径工具是否正确工作"""
    print("\n🔍 测试路径工具...")
    
    try:
        import path_utils
        print("✅ path_utils - 导入成功")
        
        # 测试get_resource_path函数
        if hasattr(path_utils, 'get_resource_path'):
            test_path = path_utils.get_resource_path("config.json")
            print(f"✅ get_resource_path - 函数存在")
            print(f"📁 config.json路径: {test_path}")
            
            # 检查config.json是否存在
            if os.path.exists(test_path):
                print("✅ config.json - 文件存在")
                return True
            else:
                print("❌ config.json - 文件不存在")
                return False
        else:
            print("❌ get_resource_path - 函数不存在")
            return False
            
    except ImportError as e:
        print(f"❌ path_utils - 导入失败: {e}")
        return False

def test_gui_startup():
    """测试GUI是否能正常启动（不显示窗口）"""
    print("\n🔍 测试GUI启动...")
    
    try:
        # 导入GUI模块
        import wechat_automation_gui
        print("✅ wechat_automation_gui - 导入成功")
        
        # 检查是否有WeChatAutomationGUI类
        if hasattr(wechat_automation_gui, 'WeChatAutomationGUI'):
            print("✅ WeChatAutomationGUI类 - 存在")
            return True
        else:
            print("❌ WeChatAutomationGUI类 - 不存在")
            return False
            
    except ImportError as e:
        print(f"❌ wechat_automation_gui - 导入失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🧪 微信自动化精简版 - modules目录测试")
    print("=" * 60)
    
    # 显示当前环境信息
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 当前目录: {os.getcwd()}")
    print(f"📦 sys.path包含: {len(sys.path)} 个路径")
    
    # 检查是否在打包环境中
    if hasattr(sys, '_MEIPASS'):
        print(f"📦 打包环境: {sys._MEIPASS}")
    else:
        print("🔧 开发环境")
    
    print("\n" + "=" * 60)
    
    # 执行测试
    tests = [
        ("modules模块导入", test_modules_import),
        ("主控制器", test_main_controller),
        ("路径工具", test_path_utils),
        ("GUI启动", test_gui_startup),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 开始测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # 显示总结
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！modules目录问题已解决！")
        print("💡 程序应该能够完整执行所有6个自动化步骤")
        return 0
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
        return 1

if __name__ == "__main__":
    sys.exit(main())
