2025-08-04 04:50:08,074 - main_controller - INFO - [START] 开始初始化主控制器...
2025-08-04 04:50:08,089 - main_controller - INFO - [LIST] 正在初始化配置管理器...
2025-08-04 04:50:08,091 - main_controller - INFO - [OK] 配置管理器初始化完成
2025-08-04 04:50:08,094 - main_controller - INFO - [DATA] 正在初始化数据管理器...
2025-08-04 04:50:08,166 - main_controller - INFO - [OK] 数据管理器初始化完成
2025-08-04 04:50:08,191 - main_controller - INFO - ⏱️ 正在初始化频率错误处理器...
2025-08-04 04:50:08,353 - main_controller - INFO - [OK] 频率错误处理器初始化完成
2025-08-04 04:50:08,358 - main_controller - INFO - 🪟 正在初始化窗口管理器...
2025-08-04 04:50:08,360 - modules.window_manager - INFO - [OK] 配置文件加载成功: C:\Users\<USER>\Desktop\微信7.28 - 副本 (2)\dist\微信自动化精简版\_internal\config.json
2025-08-04 04:50:08,374 - main_controller - INFO - [OK] 窗口管理器初始化完成
2025-08-04 04:50:08,386 - main_controller - INFO - [DESKTOP] 正在初始化主界面管理器...
2025-08-04 04:50:08,390 - modules.main_interface - INFO - [OK] 配置文件加载成功: config.json
2025-08-04 04:50:08,394 - modules.main_interface - INFO - [OK] 微信主界面操作模块初始化完成
2025-08-04 04:50:08,408 - main_controller - INFO - [OK] 主界面管理器初始化完成
2025-08-04 04:50:08,422 - main_controller - INFO - 👥 正在初始化自动添加好友模块...
2025-08-04 04:50:08,455 - WeChatAutoAdd - INFO - 创建截图目录: screenshots
2025-08-04 04:50:08,557 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 04:50:08,590 - main_controller - INFO - [OK] 自动添加好友模块初始化完成
2025-08-04 04:50:08,592 - main_controller - INFO - [NOTE] 正在初始化好友申请处理器...
2025-08-04 04:50:08,604 - modules.friend_request_window - INFO - [OK] 已加载配置文件: config.json
2025-08-04 04:50:08,608 - modules.friend_request_window - INFO - [TOOL] 使用固定坐标配置:
2025-08-04 04:50:08,627 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-04 04:50:08,639 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-04 04:50:08,677 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-04 04:50:08,726 - modules.friend_request_window - INFO - [OK] 微信好友申请窗口处理器初始化完成
2025-08-04 04:50:08,739 - main_controller - INFO - [OK] 好友申请处理器初始化完成
2025-08-04 04:50:08,772 - main_controller - INFO - 🔗 正在建立组件间引用关系...
2025-08-04 04:50:08,790 - modules.window_manager - INFO - [OK] 已设置频率错误处理器引用
2025-08-04 04:50:08,793 - main_controller - INFO - [OK] 组件间引用关系建立完成
2025-08-04 04:50:08,807 - main_controller - INFO - 🧠 正在启用智能检测功能...
2025-08-04 04:50:08,823 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-04 04:50:08,824 - main_controller - INFO - [OK] 智能检测功能已启用
2025-08-04 04:50:08,825 - main_controller - INFO - [SEARCH] 支持的检测方法:
2025-08-04 04:50:08,827 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-04 04:50:08,856 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-04 04:50:08,861 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-04 04:50:08,869 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-04 04:50:08,886 - main_controller - INFO - [MOUSE] 支持的点击方法:
2025-08-04 04:50:08,888 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-04 04:50:08,891 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-04 04:50:08,892 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-04 04:50:08,893 - main_controller - INFO -    4. 发送窗口消息
2025-08-04 04:50:08,899 - main_controller - INFO - [RETRY] 重试设置: 检测最多3次, 点击最多4次
2025-08-04 04:50:08,904 - main_controller - INFO - [PHONE] 已启用跨分辨率兼容性
2025-08-04 04:50:08,906 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-04 04:50:08,910 - main_controller - INFO - [OK] 智能检测功能启用完成
2025-08-04 04:50:08,922 - main_controller - INFO - ⚙️ 正在初始化执行状态...
2025-08-04 04:50:08,923 - main_controller - INFO - [OK] 执行状态初始化完成
2025-08-04 04:50:08,926 - main_controller - INFO - [CALL] 正在初始化GUI回调函数...
2025-08-04 04:50:08,927 - main_controller - INFO - [OK] GUI回调函数初始化完成
2025-08-04 04:50:08,928 - main_controller - INFO - [DATA] 正在初始化执行统计...
2025-08-04 04:50:08,969 - main_controller - INFO - [OK] 执行统计初始化完成
2025-08-04 04:50:08,988 - main_controller - INFO - [SUCCESS] 主控制器初始化完全完成！
2025-08-04 04:50:08,991 - main_controller - INFO - [OK] 微信自动化主控制器初始化完成
2025-08-04 04:50:08,993 - main_controller - INFO - 📅 当前北京时间: 2025-08-04 12:50:08
2025-08-04 04:50:08,994 - main_controller - INFO - ⏰ 时间段配置:
2025-08-04 04:50:09,008 - main_controller - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-04 04:50:09,010 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-04 04:50:09,026 - main_controller - INFO - 🕐 正在检查时间权限 - 当前北京时间: 12:50
2025-08-04 04:50:09,038 - main_controller - INFO - [LIST] 时间段配置检查:
2025-08-04 04:50:09,041 - main_controller - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-04 04:50:09,055 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-04 04:50:09,057 - main_controller - INFO - ⏰ 当前时间 12:50 不在上午时段 08:00-12:00 内
2025-08-04 04:50:09,058 - main_controller - INFO - ⏰ 当前时间 12:50 不在下午时段 14:00-23:59 内
2025-08-04 04:50:09,059 - main_controller - ERROR - [ERROR] 时间验证失败 - 当前时间 12:50 不在任何启用的时间段内
2025-08-04 04:50:09,059 - main_controller - ERROR - [ERROR] 已启用的时间段: 上午 08:00-12:00, 下午 14:00-23:59
2025-08-04 04:50:09,060 - main_controller - ERROR - [ERROR] 程序将等待到允许的时间段再执行
2025-08-04 04:50:09,060 - main_controller - WARNING - [WARNING] 当前时间不在设定的执行时间段内
2025-08-04 04:50:09,061 - main_controller - WARNING - [WARNING] 程序将在执行前进行时间验证
