2025-08-04 04:53:16,196 - main_controller - INFO - [START] 开始初始化主控制器...
2025-08-04 04:53:16,198 - main_controller - INFO - [LIST] 正在初始化配置管理器...
2025-08-04 04:53:16,208 - main_controller - INFO - [OK] 配置管理器初始化完成
2025-08-04 04:53:16,214 - main_controller - INFO - [DATA] 正在初始化数据管理器...
2025-08-04 04:53:16,258 - main_controller - INFO - [OK] 数据管理器初始化完成
2025-08-04 04:53:16,259 - main_controller - INFO - ⏱️ 正在初始化频率错误处理器...
2025-08-04 04:53:16,362 - main_controller - INFO - [OK] 频率错误处理器初始化完成
2025-08-04 04:53:16,374 - main_controller - INFO - 🪟 正在初始化窗口管理器...
2025-08-04 04:53:16,377 - modules.window_manager - INFO - [OK] 配置文件加载成功: C:\Users\<USER>\Desktop\微信7.28 - 副本 (2)\dist\微信自动化精简版\_internal\config.json
2025-08-04 04:53:16,379 - main_controller - INFO - [OK] 窗口管理器初始化完成
2025-08-04 04:53:16,382 - main_controller - INFO - [DESKTOP] 正在初始化主界面管理器...
2025-08-04 04:53:16,390 - modules.main_interface - INFO - [OK] 配置文件加载成功: config.json
2025-08-04 04:53:16,397 - modules.main_interface - INFO - [OK] 微信主界面操作模块初始化完成
2025-08-04 04:53:16,397 - main_controller - INFO - [OK] 主界面管理器初始化完成
2025-08-04 04:53:16,398 - main_controller - INFO - 👥 正在初始化自动添加好友模块...
2025-08-04 04:53:16,408 - WeChatAutoAdd - INFO - 微信自动添加朋友脚本初始化完成
2025-08-04 04:53:16,411 - main_controller - INFO - [OK] 自动添加好友模块初始化完成
2025-08-04 04:53:16,422 - main_controller - INFO - [NOTE] 正在初始化好友申请处理器...
2025-08-04 04:53:16,427 - modules.friend_request_window - INFO - [OK] 已加载配置文件: config.json
2025-08-04 04:53:16,428 - modules.friend_request_window - INFO - [TOOL] 使用固定坐标配置:
2025-08-04 04:53:16,429 - modules.friend_request_window - INFO -    📍 验证信息输入框: (960, 330)
2025-08-04 04:53:16,431 - modules.friend_request_window - INFO -    📍 备注信息输入框: (960, 450)
2025-08-04 04:53:16,432 - modules.friend_request_window - INFO -    📍 确定按钮: (910, 840)
2025-08-04 04:53:16,433 - modules.friend_request_window - INFO - [OK] 微信好友申请窗口处理器初始化完成
2025-08-04 04:53:16,450 - main_controller - INFO - [OK] 好友申请处理器初始化完成
2025-08-04 04:53:16,454 - main_controller - INFO - 🔗 正在建立组件间引用关系...
2025-08-04 04:53:16,457 - modules.window_manager - INFO - [OK] 已设置频率错误处理器引用
2025-08-04 04:53:16,458 - main_controller - INFO - [OK] 组件间引用关系建立完成
2025-08-04 04:53:16,460 - main_controller - INFO - 🧠 正在启用智能检测功能...
2025-08-04 04:53:16,461 - main_controller - INFO - 🧠 启用智能检测功能...
2025-08-04 04:53:16,463 - main_controller - INFO - [OK] 智能检测功能已启用
2025-08-04 04:53:16,465 - main_controller - INFO - [SEARCH] 支持的检测方法:
2025-08-04 04:53:16,466 - main_controller - INFO -    1. 通过子控件检测确定按钮
2025-08-04 04:53:16,482 - main_controller - INFO -    2. 通过文本识别检测确定按钮
2025-08-04 04:53:16,490 - main_controller - INFO -    3. 通过图像识别检测确定按钮
2025-08-04 04:53:16,491 - main_controller - INFO -    4. 基于相对位置计算确定按钮坐标
2025-08-04 04:53:16,492 - main_controller - INFO - [MOUSE] 支持的点击方法:
2025-08-04 04:53:16,492 - main_controller - INFO -    1. 使用Win32 API点击
2025-08-04 04:53:16,493 - main_controller - INFO -    2. 使用PyAutoGUI点击
2025-08-04 04:53:16,493 - main_controller - INFO -    3. 使用键盘Enter键
2025-08-04 04:53:16,494 - main_controller - INFO -    4. 发送窗口消息
2025-08-04 04:53:16,496 - main_controller - INFO - [RETRY] 重试设置: 检测最多3次, 点击最多4次
2025-08-04 04:53:16,498 - main_controller - INFO - [PHONE] 已启用跨分辨率兼容性
2025-08-04 04:53:16,499 - main_controller - INFO - 🪟 已启用窗口位置无关性
2025-08-04 04:53:16,524 - main_controller - INFO - [OK] 智能检测功能启用完成
2025-08-04 04:53:16,530 - main_controller - INFO - ⚙️ 正在初始化执行状态...
2025-08-04 04:53:16,559 - main_controller - INFO - [OK] 执行状态初始化完成
2025-08-04 04:53:16,560 - main_controller - INFO - [CALL] 正在初始化GUI回调函数...
2025-08-04 04:53:16,560 - main_controller - INFO - [OK] GUI回调函数初始化完成
2025-08-04 04:53:16,561 - main_controller - INFO - [DATA] 正在初始化执行统计...
2025-08-04 04:53:16,562 - main_controller - INFO - [OK] 执行统计初始化完成
2025-08-04 04:53:16,577 - main_controller - INFO - [SUCCESS] 主控制器初始化完全完成！
2025-08-04 04:53:16,594 - main_controller - INFO - [OK] 微信自动化主控制器初始化完成
2025-08-04 04:53:16,598 - main_controller - INFO - 📅 当前北京时间: 2025-08-04 12:53:16
2025-08-04 04:53:16,626 - main_controller - INFO - ⏰ 时间段配置:
2025-08-04 04:53:16,631 - main_controller - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-04 04:53:16,656 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-04 04:53:16,658 - main_controller - INFO - 🕐 正在检查时间权限 - 当前北京时间: 12:53
2025-08-04 04:53:16,660 - main_controller - INFO - [LIST] 时间段配置检查:
2025-08-04 04:53:16,664 - main_controller - INFO -    🌅 上午时段: 08:00 - 12:00 (启用: True)
2025-08-04 04:53:16,665 - main_controller - INFO -    🌇 下午时段: 14:00 - 23:59 (启用: True)
2025-08-04 04:53:16,698 - main_controller - INFO - ⏰ 当前时间 12:53 不在上午时段 08:00-12:00 内
2025-08-04 04:53:16,726 - main_controller - INFO - ⏰ 当前时间 12:53 不在下午时段 14:00-23:59 内
2025-08-04 04:53:16,727 - main_controller - ERROR - [ERROR] 时间验证失败 - 当前时间 12:53 不在任何启用的时间段内
2025-08-04 04:53:16,728 - main_controller - ERROR - [ERROR] 已启用的时间段: 上午 08:00-12:00, 下午 14:00-23:59
2025-08-04 04:53:16,730 - main_controller - ERROR - [ERROR] 程序将等待到允许的时间段再执行
2025-08-04 04:53:16,758 - main_controller - WARNING - [WARNING] 当前时间不在设定的执行时间段内
2025-08-04 04:53:16,760 - main_controller - WARNING - [WARNING] 程序将在执行前进行时间验证
